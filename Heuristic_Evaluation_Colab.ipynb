{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🔍 Enhanced Section-wise Heuristic Evaluation System for UI Elements\n", "\n", "## Gemini-Powered Section-wise Heuristic Evaluation using Nielsen's 10 Usability Principles\n", "\n", "This notebook implements an **enhanced section-wise heuristic evaluation system** that analyzes UI sections and their child elements against established usability principles:\n", "\n", "### 🔄 **Enhanced Section-wise Evaluation Workflow:**\n", "1. **Hierarchical Data Loading**: Load UI coordinates and DOM data with section-child relationships\n", "2. **Section Identification**: Automatically identify sections and their child elements\n", "3. **Contextual Evaluation**: Evaluate sections considering their child elements\n", "4. **Child Element Analysis**: Evaluate child elements within their section context\n", "5. **Gemini AI Analysis**: Use Gemini model for comprehensive section-wise usability assessment\n", "6. **Hierarchical Reporting**: Generate detailed reports organized by sections\n", "7. **Comprehensive Summary**: Provide section-wise and overall usability assessment\n", "\n", "### 📊 **Nielsen's 10 Usability Heuristics:**\n", "1. ✅ **Visibility of System Status** - Keep users informed\n", "2. ✅ **Match Between System and Real World** - Use familiar language\n", "3. ✅ **User Control and Freedom** - Provide emergency exits\n", "4. ✅ **Consistency and Standards** - Follow conventions\n", "5. ✅ **Error Prevention** - Prevent problems before they occur\n", "6. ✅ **Recognition Rather Than Recall** - Make options visible\n", "7. ✅ **Flexibility and Efficiency** - Support expert users\n", "8. ✅ **Aesthetic and Minimalist Design** - Remove irrelevant information\n", "9. ✅ **Help Users Recognize and Recover from Errors** - Clear error messages\n", "10. ✅ **Help and Documentation** - Provide accessible help\n", "\n", "### 🎯 **Enhanced Key Features:**\n", "- ✅ **Section-wise Analysis**: Evaluate UI sections with their child elements\n", "- ✅ **Hierarchical Context**: Consider parent-child relationships in evaluation\n", "- ✅ **Pure Gemini-based Evaluation**: No rule-based systems, direct AI assessment\n", "- ✅ **Contextual Child Evaluation**: Evaluate child elements within section context\n", "- ✅ **Structured Section Reports**: Detailed findings organized by sections\n", "- ✅ **Actionable Recommendations**: Section-specific and child-specific improvements\n", "- ✅ **Hierarchical Data Support**: Load data with section-child relationships\n", "- ✅ **Visual Analysis Integration**: Enhanced with screenshot-based evaluation\n", "- ✅ **Comprehensive Reporting**: Section-wise and overall usability assessment\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🚀 Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages for heuristic evaluation with image processing\n", "!pip install -q google-generativeai\n", "!pip install -q ipywidgets\n", "!pip install -q pillow\n", "!pip install -q matplotlib\n", "!pip install -q opencv-python\n", "\n", "print(\"✅ Packages for visual heuristic evaluation installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "api_keys"}, "source": ["## 🔑 API Keys Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_keys"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "import getpass\n", "\n", "# Try to get API keys from Colab secrets first\n", "try:\n", "    GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "    print(\"✅ Google API key loaded from Colab secrets\")\n", "except:\n", "    print(\"⚠️ Google API key not found in secrets. Please enter manually:\")\n", "    GOOGLE_API_KEY = getpass.getpass(\"Enter your Google API Key: \")\n", "\n", "# Set environment variable\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\n", "\n", "print(\"🔑 API key configured successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## ⚙️ Configuration Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config_class"}, "outputs": [], "source": ["# Configuration class for Heuristic Evaluation\n", "class Config:\n", "    # API Keys\n", "    GOOGLE_API_KEY = os.getenv(\"GOOGLE_API_KEY\")\n", "    \n", "    # Model configuration for heuristic evaluation with vision support\n", "    MODEL_NAME = \"gemini-1.5-pro-latest\"  # Vision-capable model\n", "    TEMPERATURE = 0.7\n", "    MAX_TOKENS = 10000\n", "    \n", "    # Vision-specific configuration\n", "    ENABLE_VISION = True\n", "    IMAGE_QUALITY = \"high\"  # high, medium, low\n", "    MAX_IMAGE_SIZE = (1024, 1024)  # Max dimensions for images sent to Gemini\n", "    \n", "    # Default file paths\n", "    DEFAULT_COORDINATES_PATH = \"coordinates.json\"\n", "    DEFAULT_ELEMENT_INFO_PATH = \"element_info.json\"\n", "    DEFAULT_SCREENSHOT_PATH = \"notioncom.png\"\n", "    \n", "    # Heuristic evaluation configuration\n", "    EXCLUDED_LABELS = [\"Non-UI Element\", \"Unknown Element\"]\n", "    \n", "    # Visual analysis configuration\n", "    ELEMENT_HIGHLIGHT_COLOR = (255, 0, 0)  # Red for highlighting\n", "    ELEMENT_BORDER_WIDTH = 3\n", "    CROP_PADDING = 10  # Padding around cropped elements\n", "\n", "config = Config()\n", "print(\"✅ Heuristic Evaluation Configuration loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## 📦 Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["import json\n", "import re\n", "import os\n", "import base64\n", "import io\n", "import google.generativeai as genai\n", "from typing import Dict, List, Any, Optional, Tuple\n", "import ipywidgets as widgets\n", "from IPython.display import display, HTML, clear_output, Image as IPImage\n", "from google.colab import files\n", "from PIL import Image, ImageDraw, ImageFont\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "import numpy as np\n", "\n", "print(\"✅ Libraries for visual heuristic evaluation imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "visual_analysis"}, "source": ["## 🖼️ Visual Analysis Utilities"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visual_utilities"}, "outputs": [], "source": ["class VisualAnalyzer:\n", "    \"\"\"Utilities for visual analysis of UI elements using screenshots\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.config = config\n", "        self.screenshot = None\n", "        self.screenshot_path = None\n", "        \n", "    def load_screenshot(self, screenshot_path: str = None) -> bool:\n", "        \"\"\"Load screenshot image for visual analysis\"\"\"\n", "        try:\n", "            if screenshot_path is None:\n", "                screenshot_path = self.config.DEFAULT_SCREENSHOT_PATH\n", "            \n", "            if os.path.exists(screenshot_path):\n", "                self.screenshot = Image.open(screenshot_path)\n", "                self.screenshot_path = screenshot_path\n", "                print(f\"✅ Screenshot loaded: {screenshot_path} ({self.screenshot.size})\")\n", "                return True\n", "            else:\n", "                print(f\"⚠️ Screenshot not found: {screenshot_path}\")\n", "                return False\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Error loading screenshot: {str(e)}\")\n", "            return False\n", "    \n", "    def crop_element(self, coordinates: Dict[str, int], padding: int = None) -> Image.Image:\n", "        \"\"\"Crop element from screenshot based on coordinates\"\"\"\n", "        if self.screenshot is None:\n", "            raise ValueError(\"No screenshot loaded. Call load_screenshot() first.\")\n", "        \n", "        if padding is None:\n", "            padding = self.config.CROP_PADDING\n", "        \n", "        # Extract coordinates\n", "        x = coordinates.get('x', 0)\n", "        y = coordinates.get('y', 0)\n", "        width = coordinates.get('width', 100)\n", "        height = coordinates.get('height', 100)\n", "        \n", "        # Calculate crop box with padding\n", "        left = max(0, x - padding)\n", "        top = max(0, y - padding)\n", "        right = min(self.screenshot.width, x + width + padding)\n", "        bottom = min(self.screenshot.height, y + height + padding)\n", "        \n", "        # Crop the element\n", "        cropped = self.screenshot.crop((left, top, right, bottom))\n", "        \n", "        return cropped\n", "    \n", "    def highlight_element(self, coordinates: Dict[str, int], label: str = \"\") -> Image.Image:\n", "        \"\"\"Create highlighted version of screenshot with element outlined\"\"\"\n", "        if self.screenshot is None:\n", "            raise ValueError(\"No screenshot loaded. Call load_screenshot() first.\")\n", "        \n", "        # Create a copy of the screenshot\n", "        highlighted = self.screenshot.copy()\n", "        draw = ImageDraw.Draw(highlighted)\n", "        \n", "        # Extract coordinates\n", "        x = coordinates.get('x', 0)\n", "        y = coordinates.get('y', 0)\n", "        width = coordinates.get('width', 100)\n", "        height = coordinates.get('height', 100)\n", "        \n", "        # Draw rectangle around element\n", "        rectangle = [(x, y), (x + width, y + height)]\n", "        draw.rectangle(\n", "            rectangle,\n", "            outline=self.config.ELEMENT_HIGHLIGHT_COLOR,\n", "            width=self.config.ELEMENT_BORDER_WIDTH\n", "        )\n", "        \n", "        # Add label if provided\n", "        if label:\n", "            try:\n", "                # Try to use a default font\n", "                font = ImageFont.load_default()\n", "                text_bbox = draw.textbbox((0, 0), label, font=font)\n", "                text_width = text_bbox[2] - text_bbox[0]\n", "                text_height = text_bbox[3] - text_bbox[1]\n", "                \n", "                # Position label above the element\n", "                label_x = x\n", "                label_y = max(0, y - text_height - 5)\n", "                \n", "                # Draw background for text\n", "                draw.rectangle(\n", "                    [(label_x, label_y), (label_x + text_width + 4, label_y + text_height + 4)],\n", "                    fill=(255, 255, 255, 200)\n", "                )\n", "                \n", "                # Draw text\n", "                draw.text((label_x + 2, label_y + 2), label, fill=(0, 0, 0), font=font)\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ Could not add label: {str(e)}\")\n", "        \n", "        return highlighted\n", "    \n", "    def create_element_visualization(self, coordinates: Dict[str, int], label: str = \"\") -> Tuple[Image.Image, Image.Image]:\n", "        \"\"\"C<PERSON> both highlighted screenshot and cropped element image\"\"\"\n", "        highlighted = self.highlight_element(coordinates, label)\n", "        cropped = self.crop_element(coordinates)\n", "        \n", "        return highlighted, cropped\n", "    \n", "    def display_element_analysis(self, coordinates: Dict[str, int], label: str = \"\"):\n", "        \"\"\"Display visual analysis of an element\"\"\"\n", "        try:\n", "            highlighted, cropped = self.create_element_visualization(coordinates, label)\n", "            \n", "            # Create subplot for side-by-side display\n", "            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "            \n", "            # Display highlighted screenshot\n", "            ax1.imshow(highlighted)\n", "            ax1.set_title(f\"Full Screenshot - {label}\" if label else \"Full Screenshot\")\n", "            ax1.axis('off')\n", "            \n", "            # Display cropped element\n", "            ax2.imshow(cropped)\n", "            ax2.set_title(f\"Element Close-up - {label}\" if label else \"Element Close-up\")\n", "            ax2.axis('off')\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "            \n", "            return highlighted, cropped\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error creating visual analysis: {str(e)}\")\n", "            return None, None\n", "    \n", "    def encode_image_for_gemini(self, image: Image.Image) -> str:\n", "        \"\"\"Encode image for Gemini Vision API\"\"\"\n", "        try:\n", "            # Resize image if it's too large\n", "            if image.size[0] > self.config.MAX_IMAGE_SIZE[0] or image.size[1] > self.config.MAX_IMAGE_SIZE[1]:\n", "                image = image.copy()\n", "                image.thumbnail(self.config.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)\n", "                print(f\"📏 Image resized to {image.size} for Gemini Vision API\")\n", "            \n", "            # Convert PIL Image to bytes\n", "            img_byte_arr = io.BytesIO()\n", "            image.save(img_byte_arr, format='PNG')\n", "            img_byte_arr = img_byte_arr.getvalue()\n", "            \n", "            # Encode to base64\n", "            img_base64 = base64.b64encode(img_byte_arr).decode('utf-8')\n", "            \n", "            return img_base64\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error encoding image: {str(e)}\")\n", "            return None\n", "    \n", "    def prepare_images_for_evaluation(self, coordinates: Dict[str, int]) -> Dict[str, Any]:\n", "        \"\"\"Prepare both full screenshot and cropped element images for Gemini Vision API\"\"\"\n", "        if self.screenshot is None:\n", "            return {\"has_images\": False, \"error\": \"No screenshot loaded\"}\n", "        \n", "        try:\n", "            # Create highlighted screenshot and cropped element\n", "            highlighted, cropped = self.create_element_visualization(coordinates)\n", "            \n", "            # Prepare images for Gemini\n", "            images_data = {\n", "                \"has_images\": True,\n", "                \"full_screenshot\": {\n", "                    \"image\": highlighted,\n", "                    \"description\": \"Full screenshot with highlighted element\"\n", "                },\n", "                \"element_closeup\": {\n", "                    \"image\": cropped,\n", "                    \"description\": \"Close-up view of the specific element\"\n", "                }\n", "            }\n", "            \n", "            return images_data\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error preparing images for evaluation: {str(e)}\")\n", "            return {\"has_images\": False, \"error\": str(e)}\n", "\n", "print(\"✅ VisualAnalyzer class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "heuristic_evaluator"}, "source": ["## 🏗️ Enhanced Heuristic Evaluator with Visual Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_evaluator_class"}, "outputs": [], "source": ["class HeuristicEvaluator:\n", "    \"\"\"\n", "    Gemini-Powered Heuristic Evaluator for UI Elements\n", "\n", "    Uses Gemini AI model to evaluate each UI element individually against established usability principles:\n", "    - Visibility of system status\n", "    - Match between system and real world\n", "    - User control and freedom\n", "    - Consistency and standards\n", "    - Error prevention\n", "    - Recognition rather than recall\n", "    - Flexibility and efficiency of use\n", "    - Aesthetic and minimalist design\n", "    - Help users recognize, diagnose, and recover from errors\n", "    - Help and documentation\n", "    \"\"\"\n", "\n", "    def __init__(self):\n", "        self.config = config\n", "\n", "        # Initialize Gemini model\n", "        genai.configure(api_key=self.config.GOOGLE_API_KEY)\n", "        self.model = genai.GenerativeModel(self.config.MODEL_NAME)\n", "\n", "        # Define heuristic principles for Gemini evaluation\n", "        self.heuristic_principles = self._get_heuristic_principles()\n", "        \n", "        print(\"✅ HeuristicEvaluator initialized with Gemini AI!\")\n", "\n", "    def _get_heuristic_principles(self) -> str:\n", "        \"\"\"Get comprehensive heuristic evaluation principles for Gemini\"\"\"\n", "        return \"\"\"\n", "        NIELSEN'S 10 USABILITY HEURISTICS FOR UI EVALUATION:\n", "\n", "        1. VISIBILITY OF SYSTEM STATUS\n", "        - The system should always keep users informed about what is going on\n", "        - Provide appropriate feedback within reasonable time\n", "        - Show loading states, progress indicators, current page/state\n", "        - Interactive elements should provide visual feedback (hover, focus, active states)\n", "\n", "        2. MATCH BETWEEN SYSTEM AND REAL WORLD\n", "        - The system should speak the users' language\n", "        - Use words, phrases and concepts familiar to the user\n", "        - Follow real-world conventions\n", "        - Make information appear in natural and logical order\n", "\n", "        3. USER CONTROL AND FREEDOM\n", "        - Users often choose system functions by mistake\n", "        - Provide clearly marked \"emergency exit\" to leave unwanted state\n", "        - Support undo and redo\n", "        - Give users control over their experience\n", "\n", "        4. CONSISTENCY AND STANDARDS\n", "        - Users should not have to wonder whether different words, situations, or actions mean the same thing\n", "        - Follow platform conventions and established design patterns\n", "        - Maintain internal consistency throughout the interface\n", "\n", "        5. ERROR PREVENTION\n", "        - Even better than good error messages is a careful design that prevents problems from occurring\n", "        - Eliminate error-prone conditions\n", "        - Present users with confirmation options before committing to important actions\n", "\n", "        6. RECOGNITION RATHER THAN RECALL\n", "        - Minimize the user's memory load\n", "        - Make objects, actions, and options visible\n", "        - User should not have to remember information from one part of the dialogue to another\n", "        - Instructions for use should be visible or easily retrievable\n", "\n", "        7. FLEXIBILITY AND EFFICIENCY OF USE\n", "        - Accelerators may speed up interaction for expert users\n", "        - Allow users to tailor frequent actions\n", "        - Provide shortcuts and customization options\n", "\n", "        8. AESTHET<PERSON> AND MINIMALIST DESIGN\n", "        - Dialogues should not contain information that is irrelevant or rarely needed\n", "        - Every extra unit of information competes with relevant units of information\n", "        - Keep interfaces clean and focused\n", "\n", "        9. HELP USERS RECOGNIZE, DIAGNOSE, AND RECOVER FROM ERRORS\n", "        - Error messages should be expressed in plain language\n", "        - Precisely indicate the problem\n", "        - Constructively suggest a solution\n", "\n", "        10. HELP AND DOCUMENTATION\n", "        - Even though it's better if the system can be used without documentation\n", "        - Provide help and documentation when needed\n", "        - Information should be easy to search and focused on user's task\n", "        \"\"\"\n", "\n", "print(\"✅ HeuristicEvaluator class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "enhance_heuristic_evaluator"}, "outputs": [], "source": ["# Enhance HeuristicEvaluator with visual analysis capabilities\n", "def enhance_with_visual_analysis(self):\n", "    \"\"\"Add visual analyzer to existing HeuristicEvaluator instance\"\"\"\n", "    if not hasattr(self, 'visual_analyzer'):\n", "        self.visual_analyzer = VisualAnalyzer()\n", "        print(\"✅ Visual analyzer added to HeuristicEvaluator!\")\n", "    else:\n", "        print(\"✅ Visual analyzer already available!\")\n", "\n", "def load_screenshot_for_evaluation(self, screenshot_path: str = None) -> bool:\n", "    \"\"\"Load screenshot for visual-enhanced heuristic evaluation\"\"\"\n", "    if not hasattr(self, 'visual_analyzer'):\n", "        self.enhance_with_visual_analysis()\n", "    \n", "    return self.visual_analyzer.load_screenshot(screenshot_path)\n", "\n", "def configure_vision(self, enable_vision: bool = True, image_quality: str = \"high\", max_image_size: tuple = (1024, 1024)):\n", "    \"\"\"Configure vision settings for Gemini evaluation\"\"\"\n", "    self.config.ENABLE_VISION = enable_vision\n", "    self.config.IMAGE_QUALITY = image_quality\n", "    self.config.MAX_IMAGE_SIZE = max_image_size\n", "    \n", "    status = \"enabled\" if enable_vision else \"disabled\"\n", "    print(f\"🔧 Vision analysis {status} for Gemini evaluations\")\n", "    if enable_vision:\n", "        print(f\"   📏 Max image size: {max_image_size}\")\n", "        print(f\"   🎨 Image quality: {image_quality}\")\n", "        print(f\"   🤖 Model: {self.config.MODEL_NAME} (vision-capable)\")\n", "    \n", "    return enable_vision\n", "\n", "def evaluate_element_with_visual(self, element_data: Dict[str, Any], coordinate_data: Dict[str, Any], show_visual: bool = True) -> Dict[str, Any]:\n", "    \"\"\"Enhanced evaluation with visual context from screenshot - NOW WITH ACTUAL IMAGE PASSING TO GEMINI\"\"\"\n", "    \n", "    # Prepare element information for Gemini\n", "    element_info = {\n", "        \"index\": coordinate_data.get(\"index\", -1),\n", "        \"label\": coordinate_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": coordinate_data.get(\"coordinates\", {}),\n", "        \"tag\": element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": element_data.get(\"text\", \"\"),\n", "        \"css_selector\": element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": element_data.get(\"attributes\", {})\n", "    }\n", "    \n", "    # Show visual analysis if requested and screenshot is available\n", "    if show_visual and hasattr(self, 'visual_analyzer') and self.visual_analyzer.screenshot is not None:\n", "        try:\n", "            print(f\"\\n🖼️ Visual Analysis for {element_info['label']}:\")\n", "            highlighted, cropped = self.visual_analyzer.display_element_analysis(\n", "                element_info['coordinates'], \n", "                element_info['label']\n", "            )\n", "        except Exception as e:\n", "            print(f\"⚠️ Could not display visual analysis: {str(e)}\")\n", "    \n", "    try:\n", "        # Check if vision is enabled and visual analyzer is available\n", "        has_visual_context = (hasattr(self, 'visual_analyzer') and \n", "                            self.visual_analyzer.screenshot is not None and \n", "                            self.config.ENABLE_VISION)\n", "        \n", "        if has_visual_context:\n", "            print(f\"🔍 Preparing images for Gemini Vision API evaluation...\")\n", "            \n", "            # Prepare images for Gemini Vision API\n", "            images_data = self.visual_analyzer.prepare_images_for_evaluation(element_info['coordinates'])\n", "            \n", "            if images_data.get('has_images', False):\n", "                # Create multimodal content with both text and images\n", "                content_parts = []\n", "                \n", "                # Add text prompt\n", "                prompt_text = self._create_visual_evaluation_prompt(element_info, with_images=True)\n", "                content_parts.append(prompt_text)\n", "                \n", "                # Add full screenshot with highlighted element\n", "                content_parts.append(images_data['full_screenshot']['image'])\n", "                content_parts.append(\"📸 FULL SCREENSHOT: This shows the complete interface with the element highlighted in red.\")\n", "                \n", "                # Add element close-up\n", "                content_parts.append(images_data['element_closeup']['image'])\n", "                content_parts.append(\"🔍 ELEMENT CLOSE-UP: This shows a detailed view of the specific element being evaluated.\")\n", "                \n", "                print(f\"📤 Sending multimodal content to Gemini (text + 2 images)...\")\n", "                \n", "                # Send multimodal content to Gemini\n", "                response = self.model.generate_content(content_parts)\n", "                \n", "                print(f\"✅ Received vision-enhanced evaluation from <PERSON>!\")\n", "                \n", "            else:\n", "                print(f\"⚠️ Could not prepare images: {images_data.get('error', 'Unknown error')}\")\n", "                # Fallback to text-only evaluation\n", "                prompt = self._create_visual_evaluation_prompt(element_info, with_images=False)\n", "                response = self.model.generate_content(prompt)\n", "        else:\n", "            print(f\"📝 Using text-only evaluation (vision disabled or no screenshot)\")\n", "            # Text-only evaluation\n", "            prompt = self._create_visual_evaluation_prompt(element_info, with_images=False)\n", "            response = self.model.generate_content(prompt)\n", "        \n", "        # Parse Gemini response into structured format\n", "        evaluation_result = self._parse_gemini_response(response.text, element_info)\n", "        \n", "        # Add visual analysis flags\n", "        evaluation_result['has_visual_context'] = has_visual_context\n", "        evaluation_result['vision_enabled'] = self.config.ENABLE_VISION\n", "        evaluation_result['images_sent_to_gemini'] = has_visual_context and images_data.get('has_images', False)\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error in visual evaluation: {str(e)}\")\n", "        # Fallback evaluation result in case of error\n", "        return {\n", "            \"element_info\": element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in visual evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"has_visual_context\": <PERSON><PERSON><PERSON>,\n", "            \"vision_enabled\": self.config.ENABLE_VISION,\n", "            \"images_sent_to_gemini\": False\n", "        }\n", "\n", "def _create_visual_evaluation_prompt(self, element_info: Dict[str, Any], with_images: bool = False) -> str:\n", "    \"\"\"Create enhanced evaluation prompt with visual context awareness\"\"\"\n", "    \n", "    element_json = json.dumps(element_info, indent=2)\n", "    \n", "    # Determine visual context based on whether images are being sent\n", "    if with_images:\n", "        visual_context = \"\\n\\n🖼️ VISUAL CONTEXT: You have been provided with actual screenshots showing this UI element. Use these images to conduct a comprehensive visual analysis alongside the technical data.\"\n", "        image_instructions = \"\"\"\n", "\n", "📸 IMAGE ANALYSIS INSTRUCTIONS:\n", "- The first image shows the full interface with the element highlighted in red\n", "- The second image shows a close-up view of the specific element\n", "- Analyze visual hierarchy, contrast, spacing, typography, colors, and overall design\n", "- Consider how the element fits within the broader interface context\n", "- Evaluate visual accessibility (contrast ratios, text size, etc.)\n", "- Assess visual feedback and interaction affordances\n", "\"\"\"\n", "    else:\n", "        visual_context = \"\\n\\n📝 VISUAL CONTEXT: No images provided - evaluate based on technical data and element properties only.\"\n", "        image_instructions = \"\"\n", "    \n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a comprehensive heuristic evaluation of a UI element with enhanced visual analysis capabilities.\n", "{visual_context}{image_instructions}\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.heuristic_principles}\n", "\n", "ELEMENT TO EVALUATE:\n", "{element_json}\n", "\n", "ENHANCED EVALUATION TASK:\n", "Analyze this UI element against ALL 10 Nielsen's usability heuristics. {'Use the provided images to conduct detailed visual analysis alongside the technical data.' if with_images else 'Focus on technical data and inferred visual properties.'}\n", "\n", "VISUAL EVALUATION FOCUS:\n", "1. **Visual Hierarchy**: How well does the element stand out or blend appropriately?\n", "2. **Visual Feedback**: Does the element provide clear visual states and feedback?\n", "3. **Aesthetic Design**: Is the element visually appealing and consistent?\n", "4. **Spatial Relationships**: How does the element relate to surrounding elements?\n", "5. **Accessibility**: Is the element visually accessible (contrast, size, etc.)?\n", "6. **User Expectations**: Does the visual design match user expectations?\n", "7. **Context Integration**: How well does the element integrate with the overall interface?\n", "8. **Visual Affordances**: Are interaction possibilities clearly communicated visually?\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Brief description of violation\",\n", "            \"reason\": \"Detailed explanation including visual aspects\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Specific actionable recommendation including visual improvements\",\n", "            \"visual_impact\": \"How visual design contributes to this violation\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this element passes\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief overall assessment including visual usability\",\n", "    \"key_recommendations\": [\n", "        \"Most important recommendations including visual improvements\"\n", "    ],\n", "    \"visual_assessment\": \"Specific evaluation of visual design aspects{'based on the provided screenshots' if with_images else 'based on technical properties'}\",\n", "    \"image_analysis\": \"{'Detailed analysis of what you observe in the provided images' if with_images else 'No images provided for analysis'}\"\n", "}}\n", "\n", "EVALUATION GUIDELINES:\n", "1. Be thorough - check ALL 10 heuristics {'with visual context from images' if with_images else 'based on technical data'}\n", "2. {'Analyze the actual visual appearance, colors, spacing, and layout from the images' if with_images else 'Infer visual properties from CSS and element attributes'}\n", "3. Evaluate visual hierarchy, contrast, and accessibility\n", "4. <PERSON><PERSON>s visual feedback and interaction states\n", "5. Consider visual consistency with design patterns\n", "6. Provide specific visual design recommendations\n", "7. Rate severity based on both functional and visual impact\n", "8. {'Use the images to identify visual issues not apparent from technical data alone' if with_images else 'Focus on technical compliance and inferred visual properties'}\n", "\n", "IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.\n", "\"\"\"\n", "    return prompt\n", "\n", "# Bind enhanced methods to the HeuristicEvaluator class\n", "HeuristicEvaluator.enhance_with_visual_analysis = enhance_with_visual_analysis\n", "HeuristicEvaluator.load_screenshot_for_evaluation = load_screenshot_for_evaluation\n", "HeuristicEvaluator.configure_vision = configure_vision\n", "HeuristicEvaluator.evaluate_element_with_visual = evaluate_element_with_visual\n", "HeuristicEvaluator._create_visual_evaluation_prompt = _create_visual_evaluation_prompt\n", "\n", "print(\"✅ HeuristicEvaluator enhanced with VISION-ENABLED visual analysis capabilities!\")\n", "print(\"🖼️ Images will now be sent to Gemini model for comprehensive visual evaluation!\")\n", "print(\"\\n📋 NEW FEATURES:\")\n", "print(\"   🔧 configure_vision() - Enable/disable vision and configure image settings\")\n", "print(\"   📸 Images automatically sent to Gemini during evaluation\")\n", "print(\"   🎯 Enhanced prompts for multimodal analysis\")\n", "print(\"   📊 Vision status tracking in evaluation results\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_evaluation_methods"}, "outputs": [], "source": ["# Add evaluation methods to HeuristicEvaluator class\n", "def evaluate_element(self, element_data: Dict[str, Any], coordinate_data: Dict[str, Any]) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Evaluate a single UI element using Gemini AI against all heuristics\n", "\n", "    Args:\n", "        element_data: Complete DOM element information\n", "        coordinate_data: Coordinate and label information\n", "\n", "    Returns:\n", "        Dictionary containing evaluation results from Gemini AI\n", "    \"\"\"\n", "    # Prepare element information for Gemini\n", "    element_info = {\n", "        \"index\": coordinate_data.get(\"index\", -1),\n", "        \"label\": coordinate_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": coordinate_data.get(\"coordinates\", {}),\n", "        \"tag\": element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": element_data.get(\"text\", \"\"),\n", "        \"css_selector\": element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": element_data.get(\"attributes\", {})\n", "    }\n", "\n", "    # Create comprehensive prompt for Gemini evaluation\n", "    prompt = self._create_evaluation_prompt(element_info)\n", "\n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.model.generate_content(prompt)\n", "\n", "        # Parse Gemini response into structured format\n", "        evaluation_result = self._parse_gemini_response(response.text, element_info)\n", "\n", "        return evaluation_result\n", "\n", "    except Exception as e:\n", "        # Fallback evaluation result in case of error\n", "        return {\n", "            \"element_info\": element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in Gemini evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\"\n", "        }\n", "\n", "def should_evaluate_element(self, coordinate_data: Dict[str, Any]) -> bool:\n", "    \"\"\"\n", "    Determine if an element should be evaluated based on its label\n", "\n", "    Args:\n", "        coordinate_data: Coordinate and label information\n", "\n", "    Returns:\n", "        Boolean indicating whether to evaluate the element\n", "    \"\"\"\n", "    label = coordinate_data.get(\"label\", \"\")\n", "    return label not in self.config.EXCLUDED_LABELS\n", "\n", "# Bind methods to the HeuristicEvaluator class\n", "HeuristicEvaluator.evaluate_element = evaluate_element\n", "HeuristicEvaluator.should_evaluate_element = should_evaluate_element\n", "\n", "print(\"✅ HeuristicEvaluator evaluation methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_prompt_methods"}, "outputs": [], "source": ["# Add prompt creation and response parsing methods to HeuristicEvaluator class\n", "def _create_evaluation_prompt(self, element_info: Dict[str, Any]) -> str:\n", "    \"\"\"Create comprehensive evaluation prompt for <PERSON>\"\"\"\n", "\n", "    element_json = json.dumps(element_info, indent=2)\n", "\n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a comprehensive heuristic evaluation of a UI element.\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.heuristic_principles}\n", "\n", "ELEMENT TO EVALUATE:\n", "{element_json}\n", "\n", "EVALUATION TASK:\n", "Analyze this UI element against ALL 10 Nielsen's usability heuristics. For each heuristic, determine if there are any violations.\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Brief description of violation\",\n", "            \"reason\": \"Detailed explanation of why this is a violation\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Specific actionable recommendation to fix this issue\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this element passes\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief overall assessment of the element's usability\",\n", "    \"key_recommendations\": [\n", "        \"Most important recommendations for improvement\"\n", "    ]\n", "}}\n", "\n", "EVALUATION GUIDELINES:\n", "1. Be thorough - check ALL 10 heuristics\n", "2. Consider the element's context, purpose, and user expectations\n", "3. Look at technical aspects: size, positioning, styling, text content\n", "4. Consider accessibility and usability best practices\n", "5. Provide specific, actionable recommendations\n", "6. Rate severity based on impact on user experience\n", "7. If no violations found for a heuristic, add it to passed_checks\n", "\n", "IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.\n", "\"\"\"\n", "    return prompt\n", "\n", "def _parse_gemini_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:\n", "    \"\"\"Parse Gemini's JSON response into structured evaluation result\"\"\"\n", "    try:\n", "        # Try to extract JSON from response\n", "        response_text = response_text.strip()\n", "\n", "        # Remove any markdown code blocks if present\n", "        if response_text.startswith(\"```json\"):\n", "            response_text = response_text[7:]\n", "        if response_text.startswith(\"```\"):\n", "            response_text = response_text[3:]\n", "        if response_text.endswith(\"```\"):\n", "            response_text = response_text[:-3]\n", "\n", "        # Parse JSON response\n", "        gemini_result = json.loads(response_text.strip())\n", "\n", "        # Structure the result according to our format\n", "        evaluation_result = {\n", "            \"element_info\": element_info,\n", "            \"violations\": gemini_result.get(\"violations\", []),\n", "            \"passed_checks\": gemini_result.get(\"passed_checks\", []),\n", "            \"overall_score\": gemini_result.get(\"overall_score\", 0),\n", "            \"recommendations\": gemini_result.get(\"key_recommendations\", []),\n", "            \"gemini_analysis\": gemini_result.get(\"summary\", \"\"),\n", "            \"evaluation_status\": \"success\"\n", "        }\n", "\n", "        return evaluation_result\n", "\n", "    except json.JSONDecodeError as e:\n", "        # Fallback: try to extract information from text response\n", "        return self._parse_text_response(response_text, element_info)\n", "    except Exception as e:\n", "        # Error fallback\n", "        return {\n", "            \"element_info\": element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error parsing Gemini response: {str(e)}\",\n", "            \"evaluation_status\": \"parse_error\"\n", "        }\n", "\n", "# Bind methods to the HeuristicEvaluator class\n", "HeuristicEvaluator._create_evaluation_prompt = _create_evaluation_prompt\n", "HeuristicEvaluator._parse_gemini_response = _parse_gemini_response\n", "\n", "print(\"✅ HeuristicEvaluator prompt and parsing methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_text_parser"}, "outputs": [], "source": ["# Add fallback text parsing method to HeuristicEvaluator class\n", "def _parse_text_response(self, response_text: str, element_info: Dict[str, Any]) -> Dict[str, Any]:\n", "    \"\"\"Fallback parser for non-JSON responses from Gemini\"\"\"\n", "    try:\n", "        # Try to extract key information from text response\n", "        violations = []\n", "        passed_checks = []\n", "        recommendations = []\n", "\n", "        # Look for violation patterns in text\n", "        lines = response_text.split('\\n')\n", "        current_violation = {}\n", "\n", "        for line in lines:\n", "            line = line.strip()\n", "            if 'violation' in line.lower() or 'issue' in line.lower():\n", "                if current_violation:\n", "                    violations.append(current_violation)\n", "                current_violation = {\n", "                    \"heuristic\": \"General Usability\",\n", "                    \"violation\": line,\n", "                    \"reason\": \"Identified by Gemini analysis\",\n", "                    \"severity\": \"medium\"\n", "                }\n", "            elif 'recommendation' in line.lower() or 'suggest' in line.lower():\n", "                recommendations.append(line)\n", "\n", "        if current_violation:\n", "            violations.append(current_violation)\n", "\n", "        # Estimate score based on violations found\n", "        score = max(0, 100 - (len(violations) * 15))\n", "\n", "        return {\n", "            \"element_info\": element_info,\n", "            \"violations\": violations,\n", "            \"passed_checks\": passed_checks,\n", "            \"overall_score\": score,\n", "            \"recommendations\": recommendations,\n", "            \"gemini_analysis\": response_text,\n", "            \"evaluation_status\": \"text_parsed\"\n", "        }\n", "\n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in text parsing: {str(e)}\",\n", "            \"evaluation_status\": \"text_parse_error\"\n", "        }\n", "\n", "# Bind method to the HeuristicEvaluator class\n", "HeuristicEvaluator._parse_text_response = _parse_text_response\n", "\n", "print(\"✅ HeuristicEvaluator text parsing method added!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## 📊 Data Loading and Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["# Data loading functions for heuristic evaluation\n", "def load_data_from_config():\n", "    \"\"\"Load coordinates and element info data from configuration file paths\"\"\"\n", "    \n", "    print(\"📁 Loading data for heuristic evaluation...\")\n", "    print(f\"📍 Coordinates file: {config.DEFAULT_COORDINATES_PATH}\")\n", "    print(f\"🏗️ Element info file: {config.DEFAULT_ELEMENT_INFO_PATH}\")\n", "    \n", "    coordinates_data = None\n", "    element_info_data = None\n", "    \n", "    try:\n", "        # Try to load coordinates.json\n", "        try:\n", "            with open(config.DEFAULT_COORDINATES_PATH, 'r', encoding='utf-8') as f:\n", "                coordinates_data = json.load(f)\n", "            print(f\"✅ Loaded coordinates data: {len(coordinates_data)} elements\")\n", "            \n", "            # Filter out excluded elements for heuristic evaluation\n", "            filtered_coords = []\n", "            for i, coord in enumerate(coordinates_data):\n", "                if coord.get('label', '') not in config.EXCLUDED_LABELS:\n", "                    coord['index'] = i  # Add index for reference\n", "                    filtered_coords.append(coord)\n", "            \n", "            print(f\"📊 Elements for evaluation: {len(filtered_coords)} (excluded {len(coordinates_data) - len(filtered_coords)} non-UI elements)\")\n", "            coordinates_data = filtered_coords\n", "            \n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_COORDINATES_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading coordinates: {e}\")\n", "        \n", "        # Try to load element_info.json\n", "        try:\n", "            with open(config.DEFAULT_ELEMENT_INFO_PATH, 'r', encoding='utf-8') as f:\n", "                element_info_data = json.load(f)\n", "            print(f\"✅ Loaded element info data: {len(element_info_data)} elements\")\n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_ELEMENT_INFO_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading element info: {e}\")\n", "        \n", "        return coordinates_data, element_info_data\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error in data loading: {e}\")\n", "        return None, None\n", "\n", "def create_fallback_data():\n", "    \"\"\"Create minimal fallback data for demonstration when files are not available\"\"\"\n", "    print(\"\\n🔄 Creating fallback demonstration data...\")\n", "    \n", "    fallback_coordinates = [\n", "        {\n", "            \"coordinates\": {\"x\": 949, \"y\": 385, \"width\": 626, \"height\": 330},\n", "            \"label\": \"Video\",\n", "            \"index\": 0\n", "        },\n", "        {\n", "            \"coordinates\": {\"x\": 323, \"y\": 451, \"width\": 602, \"height\": 128},\n", "            \"label\": \"Main Heading\",\n", "            \"index\": 1\n", "        },\n", "        {\n", "            \"coordinates\": {\"x\": 725, \"y\": 2666, \"width\": 447, \"height\": 90},\n", "            \"label\": \"Button\",\n", "            \"index\": 2\n", "        }\n", "    ]\n", "    \n", "    fallback_element_info = {\n", "        \"element_1\": {\n", "            \"tag\": \"video\",\n", "            \"text\": \"\",\n", "            \"cssSelector\": \"video.Video_video__KYz0l\",\n", "            \"xpath\": \"//*[@id='__next']/div[1]/video[1]\",\n", "            \"src\": \"https://example.com/video.mp4\",\n", "            \"classes\": [\"Video_video__KYz0l\"],\n", "            \"attributes\": {\"autoplay\": \"\", \"muted\": \"\"},\n", "            \"computedStyle\": {\"display\": \"block\", \"width\": \"626px\"}\n", "        },\n", "        \"element_2\": {\n", "            \"tag\": \"h1\",\n", "            \"text\": \"Sample Heading Text\",\n", "            \"cssSelector\": \"h1.heading\",\n", "            \"xpath\": \"//*[@id='__next']/h1[1]\",\n", "            \"classes\": [\"heading\"],\n", "            \"attributes\": {\"class\": \"heading\"},\n", "            \"computedStyle\": {\"font-size\": \"64px\", \"color\": \"rgb(25, 25, 24)\"}\n", "        },\n", "        \"element_3\": {\n", "            \"tag\": \"button\",\n", "            \"text\": \"Click Me\",\n", "            \"cssSelector\": \"button.primary\",\n", "            \"xpath\": \"//*[@id='content']/button[1]\",\n", "            \"classes\": [\"primary\", \"btn\"],\n", "            \"attributes\": {\"class\": \"primary btn\", \"type\": \"button\"},\n", "            \"computedStyle\": {\"background-color\": \"rgb(0, 123, 255)\", \"color\": \"white\"}\n", "        }\n", "    }\n", "    \n", "    print(f\"📊 Fallback coordinates: {len(fallback_coordinates)} elements\")\n", "    print(f\"🏗️ Fallback element info: {len(fallback_element_info)} elements\")\n", "    \n", "    return fallback_coordinates, fallback_element_info\n", "\n", "print(\"✅ Data loading functions defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hierarchical_data_functions"}, "outputs": [], "source": ["# Enhanced hierarchical data processing functions for section-wise evaluation\n", "def load_hierarchical_data_from_config():\n", "    \"\"\"Load coordinates and element info data with hierarchical structure support\"\"\"\n", "    \n", "    print(\"📁 Loading hierarchical data for section-wise heuristic evaluation...\")\n", "    print(f\"📍 Coordinates file: {config.DEFAULT_COORDINATES_PATH}\")\n", "    print(f\"🏗️ Element info file: {config.DEFAULT_ELEMENT_INFO_PATH}\")\n", "    \n", "    coordinates_data = None\n", "    element_info_data = None\n", "    \n", "    try:\n", "        # Try to load coordinates.json (hierarchical structure)\n", "        try:\n", "            with open(config.DEFAULT_COORDINATES_PATH, 'r', encoding='utf-8') as f:\n", "                coordinates_data = json.load(f)\n", "            print(f\"✅ Loaded hierarchical coordinates data: {len(coordinates_data)} root elements\")\n", "            \n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_COORDINATES_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading coordinates: {e}\")\n", "        \n", "        # Try to load element_info.json (with children data)\n", "        try:\n", "            with open(config.DEFAULT_ELEMENT_INFO_PATH, 'r', encoding='utf-8') as f:\n", "                element_info_data = json.load(f)\n", "            print(f\"✅ Loaded hierarchical element info data: {len(element_info_data)} elements\")\n", "        except FileNotFoundError:\n", "            print(f\"⚠️ {config.DEFAULT_ELEMENT_INFO_PATH} not found. You can upload your own file below.\")\n", "        except Exception as e:\n", "            print(f\"❌ Error loading element info: {e}\")\n", "        \n", "        return coordinates_data, element_info_data\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error in data loading: {e}\")\n", "        return None, None\n", "\n", "def extract_sections_and_children(coordinates_data):\n", "    \"\"\"Extract sections and their child elements from hierarchical data\"\"\"\n", "    \n", "    sections = []\n", "    all_elements = []\n", "    \n", "    def process_element(element_key, element_data, parent_section=None, depth=0):\n", "        \"\"\"Recursively process elements and identify sections\"\"\"\n", "        \n", "        element_info = {\n", "            'key': element_key,\n", "            'data': element_data,\n", "            'parent_section': parent_section,\n", "            'depth': depth,\n", "            'is_section': 'Section' in element_data.get('label', ''),\n", "            'children': []\n", "        }\n", "        \n", "        # If this is a section, track it\n", "        if element_info['is_section']:\n", "            sections.append(element_info)\n", "            current_section = element_info\n", "        else:\n", "            current_section = parent_section\n", "        \n", "        # Process children recursively\n", "        children_data = element_data.get('children', [])\n", "        for i, child in enumerate(children_data):\n", "            child_key = f\"{element_key}_child_{i}\"\n", "            child_info = process_element(child_key, child, current_section, depth + 1)\n", "            element_info['children'].append(child_info)\n", "        \n", "        all_elements.append(element_info)\n", "        return element_info\n", "    \n", "    # Process all root elements\n", "    for element_key, element_data in coordinates_data.items():\n", "        process_element(element_key, element_data)\n", "    \n", "    print(f\"📊 Extracted {len(sections)} sections with hierarchical structure\")\n", "    print(f\"🏗️ Total elements processed: {len(all_elements)}\")\n", "    \n", "    return sections, all_elements\n", "\n", "def get_all_child_elements(element_info, include_excluded=False):\n", "    \"\"\"Get all child elements from a section, optionally including excluded elements\"\"\"\n", "    \n", "    child_elements = []\n", "    \n", "    def collect_children(element):\n", "        \"\"\"Recursively collect all child elements\"\"\"\n", "        for child in element.get('children', []):\n", "            # Check if element should be included\n", "            child_label = child.get('data', {}).get('label', '')\n", "            \n", "            if include_excluded or child_label not in config.EXCLUDED_LABELS:\n", "                child_elements.append(child)\n", "            \n", "            # Recursively collect from this child's children\n", "            collect_children(child)\n", "    \n", "    collect_children(element_info)\n", "    return child_elements\n", "\n", "print(\"✅ Enhanced hierarchical data processing functions defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "two_level_evaluation_functions"}, "outputs": [], "source": ["# Two-Level Evaluation System Functions\n", "def extract_sections_with_two_levels(coordinates_data):\n", "    \"\"\"Extract sections with two-level structure: Level 0 (sections) and Level 1 (immediate children)\"\"\"\n", "    \n", "    sections = []\n", "    all_elements = []\n", "    \n", "    def process_element(element_key, element_data, parent_section=None, depth=0):\n", "        \"\"\"Recursively process elements and identify sections with two-level structure\"\"\"\n", "        \n", "        element_info = {\n", "            'key': element_key,\n", "            'data': element_data,\n", "            'parent_section': parent_section,\n", "            'depth': depth,\n", "            'is_section': 'Section' in element_data.get('label', ''),\n", "            'children': [],\n", "            'immediate_children': [],  # Level 1: Direct children to be evaluated\n", "            'nested_children': [],     # Level 2+: Nested children for context only\n", "            'evaluation_level': None   # Will be set to 0 for sections, 1 for immediate children\n", "        }\n", "        \n", "        # If this is a section, track it as Level 0\n", "        if element_info['is_section']:\n", "            sections.append(element_info)\n", "            element_info['evaluation_level'] = 0  # Level 0: Section evaluation\n", "            current_section = element_info\n", "        else:\n", "            current_section = parent_section\n", "        \n", "        # Process children recursively\n", "        children_data = element_data.get('children', [])\n", "        for i, child in enumerate(children_data):\n", "            child_key = f\"{element_key}_child_{i}\"\n", "            child_info = process_element(child_key, child, current_section, depth + 1)\n", "            element_info['children'].append(child_info)\n", "            \n", "            # For sections, categorize children by evaluation level\n", "            if element_info['is_section']:\n", "                # This is an immediate child (Level 1) - will be evaluated\n", "                child_info['evaluation_level'] = 1  # Level 1: Immediate child evaluation\n", "                element_info['immediate_children'].append(child_info)\n", "                \n", "                # Collect all nested children (Level 2+) for context only\n", "                nested_children = get_all_nested_children_for_context(child_info)\n", "                element_info['nested_children'].extend(nested_children)\n", "        \n", "        all_elements.append(element_info)\n", "        return element_info\n", "    \n", "    # Process all root elements\n", "    for element_key, element_data in coordinates_data.items():\n", "        process_element(element_key, element_data)\n", "    \n", "    print(f\"📊 Extracted {len(sections)} sections with two-level evaluation structure\")\n", "    print(f\"🏗️ Total elements processed: {len(all_elements)}\")\n", "    \n", "    # Print detailed section structure summary\n", "    print(f\"\\n📋 Two-Level Evaluation Structure:\")\n", "    for i, section in enumerate(sections):\n", "        section_label = section['data'].get('label', 'Unknown Section')\n", "        immediate_count = len(section['immediate_children'])\n", "        nested_count = len(section['nested_children'])\n", "        print(f\"  {i+1}. 📋 Level 0 - {section_label}:\")\n", "        print(f\"     🔍 Level 1 - {immediate_count} immediate children (will be evaluated)\")\n", "        print(f\"     📝 Level 2+ - {nested_count} nested children (context only)\")\n", "    \n", "    return sections, all_elements\n", "\n", "def get_all_nested_children_for_context(element_info):\n", "    \"\"\"Get all nested children (Level 2+) from an element for context only\"\"\"\n", "    \n", "    nested_children = []\n", "    \n", "    def collect_nested(element, current_depth=0):\n", "        \"\"\"Recursively collect all nested children for context\"\"\"\n", "        for child in element.get('children', []):\n", "            # Mark as context-only (not for individual evaluation)\n", "            child['evaluation_level'] = 'context_only'\n", "            nested_children.append(child)\n", "            # Continue collecting from deeper levels\n", "            collect_nested(child, current_depth + 1)\n", "    \n", "    collect_nested(element_info)\n", "    return nested_children\n", "\n", "def get_immediate_children_for_evaluation(section_info):\n", "    \"\"\"Get immediate children (Level 1) that need individual evaluation\"\"\"\n", "    \n", "    immediate_children = []\n", "    \n", "    for child in section_info.get('immediate_children', []):\n", "        # Only include children that should be evaluated (not excluded)\n", "        child_label = child.get('data', {}).get('label', '')\n", "        if child_label not in config.EXCLUDED_LABELS:\n", "            immediate_children.append(child)\n", "    \n", "    return immediate_children\n", "\n", "def get_nested_children_context(section_info):\n", "    \"\"\"Get nested children (Level 2+) as context for evaluation\"\"\"\n", "    \n", "    context_children = []\n", "    \n", "    for child in section_info.get('nested_children', []):\n", "        child_data = child.get('data', {})\n", "        context_info = {\n", "            'label': child_data.get('label', 'Unknown'),\n", "            'coordinates': child_data.get('coordinates', {}),\n", "            'depth': child.get('depth', 0),\n", "            'evaluation_level': 'context_only'\n", "        }\n", "        context_children.append(context_info)\n", "    \n", "    return context_children\n", "\n", "print(\"✅ Two-Level Evaluation System functions defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "heuristic_system"}, "source": ["## 🎯 Enhanced Section-wise Heuristic Evaluation System"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "heuristic_system_class"}, "outputs": [], "source": ["class HeuristicEvaluationSystem:\n", "    \"\"\"Main system for conducting heuristic evaluations\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.evaluator = HeuristicEvaluator()\n", "        self.coordinates_data = []\n", "        self.element_info_data = {}\n", "        self.evaluation_results = []\n", "        \n", "        print(\"✅ Heuristic Evaluation System initialized!\")\n", "    \n", "    def load_data(self, coordinates_data=None, element_info_data=None):\n", "        \"\"\"Load data for evaluation\"\"\"\n", "        if coordinates_data is None or element_info_data is None:\n", "            # Try to load from configuration files\n", "            coordinates_data, element_info_data = load_data_from_config()\n", "            \n", "            # Use fallback data if files are not available\n", "            if coordinates_data is None or element_info_data is None:\n", "                print(\"\\n⚠️ Configuration files not found. Using fallback data for demonstration.\")\n", "                print(\"💡 You can upload your own files using the upload interface below.\")\n", "                coordinates_data, element_info_data = create_fallback_data()\n", "        \n", "        self.coordinates_data = coordinates_data\n", "        self.element_info_data = element_info_data\n", "        \n", "        print(f\"\\n✅ Data loading complete!\")\n", "        print(f\"📊 Coordinates: {len(self.coordinates_data)} elements\")\n", "        print(f\"🏗️ Element info: {len(self.element_info_data)} elements\")\n", "        \n", "        return True\n", "    \n", "    def perform_evaluation(self):\n", "        \"\"\"Perform heuristic evaluation on all loaded elements\"\"\"\n", "        if not self.coordinates_data or not self.element_info_data:\n", "            print(\"❌ No data loaded. Please load data first.\")\n", "            return False\n", "        \n", "        print(\"\\n🚀 Starting Heuristic Evaluation...\")\n", "        print(\"=\" * 60)\n", "        \n", "        self.evaluation_results = []\n", "        \n", "        for i, coord_data in enumerate(self.coordinates_data):\n", "            # Check if element should be evaluated\n", "            if not self.evaluator.should_evaluate_element(coord_data):\n", "                print(f\"⏭️ Skipping {coord_data.get('label', 'Unknown')} (excluded element)\")\n", "                continue\n", "            \n", "            # Get corresponding element info\n", "            element_key = f\"element_{coord_data.get('index', i) + 1}\"\n", "            element_data = self.element_info_data.get(element_key, {})\n", "            \n", "            print(f\"\\n🔍 Evaluating Element {i+1}: {coord_data.get('label', 'Unknown')}\")\n", "            print(f\"📍 Position: ({coord_data.get('coordinates', {}).get('x', 0)}, {coord_data.get('coordinates', {}).get('y', 0)})\")\n", "            \n", "            # Perform evaluation\n", "            try:\n", "                result = self.evaluator.evaluate_element(element_data, coord_data)\n", "                self.evaluation_results.append(result)\n", "                \n", "                # Display quick summary\n", "                status = result.get('evaluation_status', 'unknown')\n", "                score = result.get('overall_score', 0)\n", "                violations = len(result.get('violations', []))\n", "                \n", "                print(f\"✅ Evaluation complete - Status: {status}, Score: {score}/100, Violations: {violations}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error evaluating element: {str(e)}\")\n", "                continue\n", "        \n", "        print(f\"\\n🎉 Heuristic evaluation completed!\")\n", "        print(f\"📊 Evaluated {len(self.evaluation_results)} elements\")\n", "        \n", "        return True\n", "    \n", "    def generate_report(self):\n", "        \"\"\"Generate comprehensive heuristic evaluation report\"\"\"\n", "        if not self.evaluation_results:\n", "            print(\"❌ No evaluation results available. Please run evaluation first.\")\n", "            return None\n", "        \n", "        print(\"\\n📋 HEURISTIC EVALUATION REPORT\")\n", "        print(\"=\" * 80)\n", "        \n", "        # Overall statistics\n", "        total_elements = len(self.evaluation_results)\n", "        total_violations = sum(len(result.get('violations', [])) for result in self.evaluation_results)\n", "        avg_score = sum(result.get('overall_score', 0) for result in self.evaluation_results) / total_elements if total_elements > 0 else 0\n", "        \n", "        print(f\"\\n📊 OVERALL STATISTICS:\")\n", "        print(f\"   • Elements Evaluated: {total_elements}\")\n", "        print(f\"   • Total Violations Found: {total_violations}\")\n", "        print(f\"   • Average Usability Score: {avg_score:.1f}/100\")\n", "        \n", "        # Severity breakdown\n", "        severity_counts = {'high': 0, 'medium': 0, 'low': 0}\n", "        for result in self.evaluation_results:\n", "            for violation in result.get('violations', []):\n", "                severity = violation.get('severity', 'medium').lower()\n", "                if severity in severity_counts:\n", "                    severity_counts[severity] += 1\n", "        \n", "        print(f\"\\n🚨 VIOLATION SEVERITY BREAKDOWN:\")\n", "        print(f\"   • High Severity: {severity_counts['high']}\")\n", "        print(f\"   • Medium Severity: {severity_counts['medium']}\")\n", "        print(f\"   • Low Severity: {severity_counts['low']}\")\n", "        \n", "        # Element-by-element results\n", "        print(f\"\\n🔍 DETAILED ELEMENT ANALYSIS:\")\n", "        print(\"-\" * 80)\n", "        \n", "        for i, result in enumerate(self.evaluation_results):\n", "            element_info = result.get('element_info', {})\n", "            label = element_info.get('label', 'Unknown')\n", "            score = result.get('overall_score', 0)\n", "            violations = result.get('violations', [])\n", "            \n", "            print(f\"\\n{i+1}. {label} (Score: {score}/100)\")\n", "            \n", "            if violations:\n", "                print(f\"   ❌ Violations Found ({len(violations)}):\")\n", "                for violation in violations:\n", "                    heuristic = violation.get('heuristic', 'Unknown')\n", "                    severity = violation.get('severity', 'medium')\n", "                    description = violation.get('violation', 'No description')\n", "                    print(f\"      • [{severity.upper()}] {heuristic}: {description}\")\n", "            else:\n", "                print(f\"   ✅ No violations found\")\n", "        \n", "        # Top recommendations\n", "        all_recommendations = []\n", "        for result in self.evaluation_results:\n", "            all_recommendations.extend(result.get('recommendations', []))\n", "        \n", "        if all_recommendations:\n", "            print(f\"\\n💡 TOP RECOMMENDATIONS:\")\n", "            print(\"-\" * 80)\n", "            for i, rec in enumerate(all_recommendations[:10], 1):  # Top 10\n", "                print(f\"{i}. {rec}\")\n", "        \n", "        print(\"\\n\" + \"=\" * 80)\n", "        print(\"📋 End of Heuristic Evaluation Report\")\n", "        \n", "        return {\n", "            'total_elements': total_elements,\n", "            'total_violations': total_violations,\n", "            'average_score': avg_score,\n", "            'severity_counts': severity_counts,\n", "            'detailed_results': self.evaluation_results\n", "        }\n", "\n", "print(\"✅ HeuristicEvaluationSystem class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "section_wise_system"}, "outputs": [], "source": ["class SectionWiseHeuristicEvaluationSystem:\n", "    \"\"\"Enhanced system for conducting section-wise heuristic evaluations with child elements\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.evaluator = HeuristicEvaluator()\n", "        self.coordinates_data = {}\n", "        self.element_info_data = {}\n", "        self.sections = []\n", "        self.all_elements = []\n", "        self.evaluation_results = []\n", "        self.section_evaluations = []\n", "        \n", "        print(\"✅ Section-wise Heuristic Evaluation System initialized!\")\n", "    \n", "    def load_hierarchical_data(self, coordinates_data=None, element_info_data=None):\n", "        \"\"\"Load hierarchical data for section-wise evaluation\"\"\"\n", "        if coordinates_data is None or element_info_data is None:\n", "            # Try to load from configuration files\n", "            coordinates_data, element_info_data = load_hierarchical_data_from_config()\n", "            \n", "            # Use fallback data if files are not available\n", "            if coordinates_data is None or element_info_data is None:\n", "                print(\"\\n⚠️ Configuration files not found. Using fallback data for demonstration.\")\n", "                print(\"💡 You can upload your own files using the upload interface below.\")\n", "                coordinates_data, element_info_data = create_fallback_data()\n", "        \n", "        self.coordinates_data = coordinates_data\n", "        self.element_info_data = element_info_data\n", "        \n", "        # Extract sections and hierarchical structure\n", "        if coordinates_data:\n", "            self.sections, self.all_elements = extract_sections_and_children(coordinates_data)\n", "        \n", "        print(f\"\\n✅ Hierarchical data loading complete!\")\n", "        print(f\"📊 Root elements: {len(self.coordinates_data)} elements\")\n", "        print(f\"🏗️ Element info: {len(self.element_info_data)} elements\")\n", "        print(f\"📋 Sections identified: {len(self.sections)} sections\")\n", "        print(f\"🔍 Total elements in hierarchy: {len(self.all_elements)} elements\")\n", "        \n", "        return True\n", "    \n", "    def evaluate_section_with_children(self, section_info, show_visual=True):\n", "        \"\"\"Evaluate a section considering all its child elements\"\"\"\n", "        \n", "        section_key = section_info['key']\n", "        section_data = section_info['data']\n", "        section_label = section_data.get('label', 'Unknown Section')\n", "        \n", "        print(f\"\\n🏗️ Evaluating Section: {section_label}\")\n", "        print(f\"📍 Section Key: {section_key}\")\n", "        print(f\"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})\")\n", "        \n", "        # Get all child elements for this section\n", "        child_elements = get_all_child_elements(section_info, include_excluded=False)\n", "        \n", "        print(f\"👥 Child elements found: {len(child_elements)}\")\n", "        \n", "        # Evaluate the section itself\n", "        section_element_data = self.element_info_data.get(section_key, {})\n", "        \n", "        # Create coordinate data for section evaluation\n", "        section_coord_data = {\n", "            'coordinates': section_data.get('coordinates', {}),\n", "            'label': section_label,\n", "            'index': section_key\n", "        }\n", "        \n", "        # Evaluate section with context of its children\n", "        section_evaluation = self._evaluate_section_with_context(\n", "            section_element_data, section_coord_data, child_elements, show_visual\n", "        )\n", "        \n", "        # Evaluate each child element within section context\n", "        child_evaluations = []\n", "        for i, child in enumerate(child_elements):\n", "            child_key = child['key']\n", "            child_data = child['data']\n", "            child_label = child_data.get('label', 'Unknown Child')\n", "            \n", "            print(f\"  🔍 Evaluating child {i+1}/{len(child_elements)}: {child_label}\")\n", "            \n", "            # Get element info for child\n", "            child_element_data = self.element_info_data.get(child_key, {})\n", "            \n", "            # Create coordinate data for child evaluation\n", "            child_coord_data = {\n", "                'coordinates': child_data.get('coordinates', {}),\n", "                'label': child_label,\n", "                'index': child_key\n", "            }\n", "            \n", "            # Evaluate child with section context\n", "            child_evaluation = self._evaluate_child_with_section_context(\n", "                child_element_data, child_coord_data, section_info, show_visual\n", "            )\n", "            \n", "            child_evaluations.append(child_evaluation)\n", "        \n", "        # Combine section and child evaluations\n", "        combined_evaluation = {\n", "            'section_info': section_info,\n", "            'section_evaluation': section_evaluation,\n", "            'child_evaluations': child_evaluations,\n", "            'total_children': len(child_elements),\n", "            'section_score': section_evaluation.get('overall_score', 0),\n", "            'average_child_score': sum(ce.get('overall_score', 0) for ce in child_evaluations) / len(child_evaluations) if child_evaluations else 0,\n", "            'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in child_evaluations)\n", "        }\n", "        \n", "        print(f\"  ✅ Section evaluation complete - Section Score: {combined_evaluation['section_score']}/100\")\n", "        print(f\"  📊 Average Child Score: {combined_evaluation['average_child_score']:.1f}/100\")\n", "        print(f\"  🚨 Total Violations: {combined_evaluation['total_violations']}\")\n", "        \n", "        return combined_evaluation\n", "\n", "    def load_two_level_hierarchical_data(self, coordinates_data=None, element_info_data=None):\n", "        \"\"\"Load hierarchical data for two-level section-wise evaluation\"\"\"\n", "        if coordinates_data is None or element_info_data is None:\n", "            # Try to load from configuration files\n", "            coordinates_data, element_info_data = load_hierarchical_data_from_config()\n", "            \n", "            # Use fallback data if files are not available\n", "            if coordinates_data is None or element_info_data is None:\n", "                print(\"\\n⚠️ Configuration files not found. Using fallback data for demonstration.\")\n", "                print(\"💡 You can upload your own files using the upload interface below.\")\n", "                coordinates_data, element_info_data = create_fallback_data()\n", "        \n", "        self.coordinates_data = coordinates_data\n", "        self.element_info_data = element_info_data\n", "        \n", "        # Extract sections with two-level hierarchical structure\n", "        if coordinates_data:\n", "            self.sections, self.all_elements = extract_sections_with_two_levels(coordinates_data)\n", "        \n", "        print(f\"\\n✅ Two-Level hierarchical data loading complete!\")\n", "        print(f\"📊 Root elements: {len(self.coordinates_data)} elements\")\n", "        print(f\"🏗️ Element info: {len(self.element_info_data)} elements\")\n", "        print(f\"📋 Sections identified: {len(self.sections)} sections\")\n", "        print(f\"🔍 Total elements in hierarchy: {len(self.all_elements)} elements\")\n", "        \n", "        return True\n", "    \n", "    def evaluate_section_two_level(self, section_info, show_visual=True):\n", "        \"\"\"Evaluate a section using two-level approach: Level 0 (section) and Level 1 (immediate children)\"\"\"\n", "        \n", "        section_key = section_info['key']\n", "        section_data = section_info['data']\n", "        section_label = section_data.get('label', 'Unknown Section')\n", "        \n", "        print(f\"\\n🏗️ Two-Level Evaluation for Section: {section_label}\")\n", "        print(f\"📍 Section Key: {section_key}\")\n", "        print(f\"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})\")\n", "        \n", "        # Get immediate children (Level 1) for evaluation\n", "        immediate_children = get_immediate_children_for_evaluation(section_info)\n", "        \n", "        # Get nested children (Level 2+) for context only\n", "        nested_children_context = get_nested_children_context(section_info)\n", "        \n", "        print(f\"🔍 Level 1 - Immediate children to evaluate: {len(immediate_children)}\")\n", "        print(f\"📝 Level 2+ - Nested children for context: {len(nested_children_context)}\")\n", "        \n", "        # Show visual analysis for the section if available\n", "        if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:\n", "            try:\n", "                print(f\"\\n🖼️ Level 0 Visual Analysis - Section: {section_label}\")\n", "                section_coords = section_data.get('coordinates', {})\n", "                highlighted, cropped = self.evaluator.visual_analyzer.display_element_analysis(\n", "                    section_coords, f\"Level 0 - Section: {section_label}\"\n", "                )\n", "            except Exception as e:\n", "                print(f\"⚠️ Could not display section visual analysis: {str(e)}\")\n", "        \n", "        # LEVEL 0: Evaluate the section itself\n", "        print(f\"\\n📋 Level 0 Evaluation - Section as a whole\")\n", "        section_element_data = self.element_info_data.get(section_key, {})\n", "        \n", "        section_coord_data = {\n", "            'coordinates': section_data.get('coordinates', {}),\n", "            'label': section_label,\n", "            'index': section_key\n", "        }\n", "        \n", "        # Evaluate section with context of immediate and nested children\n", "        section_evaluation = self._evaluate_section_two_level(\n", "            section_element_data, section_coord_data, immediate_children, nested_children_context, show_visual\n", "        )\n", "        \n", "        # LEVEL 1: Evaluate immediate children individually\n", "        print(f\"\\n🔍 Level 1 Evaluation - Immediate children\")\n", "        level1_evaluations = []\n", "        \n", "        for i, child in enumerate(immediate_children):\n", "            child_key = child['key']\n", "            child_data = child['data']\n", "            child_label = child_data.get('label', 'Unknown Child')\n", "            \n", "            print(f\"  🔍 Level 1 - Evaluating {i+1}/{len(immediate_children)}: {child_label}\")\n", "            \n", "            # Show visual analysis for immediate child if available\n", "            if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:\n", "                try:\n", "                    print(f\"    🖼️ Level 1 Visual Analysis - Child: {child_label}\")\n", "                    child_coords = child_data.get('coordinates', {})\n", "                    child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(\n", "                        child_coords, f\"Level 1 - Child: {child_label}\"\n", "                    )\n", "                except Exception as e:\n", "                    print(f\"    ⚠️ Could not display child visual analysis: {str(e)}\")\n", "            \n", "            # Get nested children of this immediate child for context\n", "            child_nested_context = get_all_nested_children_for_context(child)\n", "            \n", "            # Get element info for immediate child\n", "            child_element_data = self.element_info_data.get(child_key, {})\n", "            \n", "            child_coord_data = {\n", "                'coordinates': child_data.get('coordinates', {}),\n", "                'label': child_label,\n", "                'index': child_key\n", "            }\n", "            \n", "            # Evaluate immediate child with section context and its nested children as context\n", "            child_evaluation = self._evaluate_level1_child_with_context(\n", "                child_element_data, child_coord_data, section_info, child_nested_context, show_visual\n", "            )\n", "            \n", "            level1_evaluations.append(child_evaluation)\n", "        \n", "        # Combine Level 0 and Level 1 evaluations\n", "        combined_evaluation = {\n", "            'section_info': section_info,\n", "            'level0_evaluation': section_evaluation,  # Section evaluation\n", "            'level1_evaluations': level1_evaluations,  # Immediate children evaluations\n", "            'total_level1_children': len(immediate_children),\n", "            'total_context_children': len(nested_children_context),\n", "            'level0_score': section_evaluation.get('overall_score', 0),\n", "            'average_level1_score': sum(ce.get('overall_score', 0) for ce in level1_evaluations) / len(level1_evaluations) if level1_evaluations else 0,\n", "            'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in level1_evaluations),\n", "            'evaluation_approach': 'two_level',\n", "            'has_visual_context': show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "        }\n", "        \n", "        print(f\"\\n  ✅ Two-Level evaluation complete:\")\n", "        print(f\"    📋 Level 0 (Section) Score: {combined_evaluation['level0_score']}/100\")\n", "        print(f\"    🔍 Level 1 (Avg Children) Score: {combined_evaluation['average_level1_score']:.1f}/100\")\n", "        print(f\"    🚨 Total Violations: {combined_evaluation['total_violations']}\")\n", "        print(f\"    📊 Children Evaluated: {combined_evaluation['total_level1_children']}\")\n", "        print(f\"    📝 Context Children: {combined_evaluation['total_context_children']}\")\n", "        \n", "        return combined_evaluation\n", "\n", "print(\"✅ SectionWiseHeuristicEvaluationSystem class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visual_enhanced_section_methods"}, "outputs": [], "source": ["# Enhanced visual evaluation methods for section-wise system\n", "def evaluate_section_with_children_visual(self, section_info, show_visual=True):\n", "    \"\"\"Evaluate a section with visual-enhanced analysis considering all its child elements\"\"\"\n", "    \n", "    section_key = section_info['key']\n", "    section_data = section_info['data']\n", "    section_label = section_data.get('label', 'Unknown Section')\n", "    \n", "    print(f\"\\n🏗️ Evaluating Section with Visual Analysis: {section_label}\")\n", "    print(f\"📍 Section Key: {section_key}\")\n", "    print(f\"📊 Section Position: ({section_data.get('coordinates', {}).get('x', 0)}, {section_data.get('coordinates', {}).get('y', 0)})\")\n", "    \n", "    # Get all child elements for this section\n", "    child_elements = get_all_child_elements(section_info, include_excluded=False)\n", "    \n", "    print(f\"👥 Child elements found: {len(child_elements)}\")\n", "    \n", "    # Show visual analysis for the section if available\n", "    if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:\n", "        try:\n", "            print(f\"\\n🖼️ Visual Analysis for Section: {section_label}\")\n", "            section_coords = section_data.get('coordinates', {})\n", "            highlighted, cropped = self.evaluator.visual_analyzer.display_element_analysis(\n", "                section_coords, f\"Section: {section_label}\"\n", "            )\n", "        except Exception as e:\n", "            print(f\"⚠️ Could not display section visual analysis: {str(e)}\")\n", "    \n", "    # Evaluate the section itself with visual context\n", "    section_element_data = self.element_info_data.get(section_key, {})\n", "    \n", "    # Create coordinate data for section evaluation\n", "    section_coord_data = {\n", "        'coordinates': section_data.get('coordinates', {}),\n", "        'label': section_label,\n", "        'index': section_key\n", "    }\n", "    \n", "    # Evaluate section with visual context and children information\n", "    section_evaluation = self._evaluate_section_with_visual_context(\n", "        section_element_data, section_coord_data, child_elements, show_visual\n", "    )\n", "    \n", "    # Determine evaluation strategy for children\n", "    child_evaluations = []\n", "    children_need_individual_eval = self._should_evaluate_children_individually(\n", "        section_evaluation, child_elements\n", "    )\n", "    \n", "    if children_need_individual_eval:\n", "        print(f\"  🔍 Individual evaluation needed for child elements\")\n", "        # Evaluate each child element individually with visual analysis\n", "        for i, child in enumerate(child_elements):\n", "            child_key = child['key']\n", "            child_data = child['data']\n", "            child_label = child_data.get('label', 'Unknown Child')\n", "            \n", "            print(f\"    🔍 Evaluating child {i+1}/{len(child_elements)}: {child_label}\")\n", "            \n", "            # Show visual analysis for child if available\n", "            if show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None:\n", "                try:\n", "                    print(f\"      🖼️ Visual Analysis for Child: {child_label}\")\n", "                    child_coords = child_data.get('coordinates', {})\n", "                    child_highlighted, child_cropped = self.evaluator.visual_analyzer.display_element_analysis(\n", "                        child_coords, f\"Child: {child_label}\"\n", "                    )\n", "                except Exception as e:\n", "                    print(f\"      ⚠️ Could not display child visual analysis: {str(e)}\")\n", "            \n", "            # Get element info for child\n", "            child_element_data = self.element_info_data.get(child_key, {})\n", "            \n", "            # Create coordinate data for child evaluation\n", "            child_coord_data = {\n", "                'coordinates': child_data.get('coordinates', {}),\n", "                'label': child_label,\n", "                'index': child_key\n", "            }\n", "            \n", "            # Evaluate child with visual context and section context\n", "            child_evaluation = self._evaluate_child_with_visual_section_context(\n", "                child_element_data, child_coord_data, section_info, show_visual\n", "            )\n", "            \n", "            child_evaluations.append(child_evaluation)\n", "    else:\n", "        print(f\"  📋 Section-based evaluation sufficient for child elements\")\n", "        # Create summary evaluations based on section analysis\n", "        for child in child_elements:\n", "            child_summary = self._create_child_summary_from_section(child, section_evaluation)\n", "            child_evaluations.append(child_summary)\n", "    \n", "    # Combine section and child evaluations\n", "    combined_evaluation = {\n", "        'section_info': section_info,\n", "        'section_evaluation': section_evaluation,\n", "        'child_evaluations': child_evaluations,\n", "        'total_children': len(child_elements),\n", "        'section_score': section_evaluation.get('overall_score', 0),\n", "        'average_child_score': sum(ce.get('overall_score', 0) for ce in child_evaluations) / len(child_evaluations) if child_evaluations else 0,\n", "        'total_violations': len(section_evaluation.get('violations', [])) + sum(len(ce.get('violations', [])) for ce in child_evaluations),\n", "        'evaluation_strategy': 'individual_children' if children_need_individual_eval else 'section_based',\n", "        'has_visual_context': show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    }\n", "    \n", "    print(f\"  ✅ Section evaluation complete - Section Score: {combined_evaluation['section_score']}/100\")\n", "    print(f\"  📊 Average Child Score: {combined_evaluation['average_child_score']:.1f}/100\")\n", "    print(f\"  🚨 Total Violations: {combined_evaluation['total_violations']}\")\n", "    print(f\"  🎯 Evaluation Strategy: {combined_evaluation['evaluation_strategy']}\")\n", "    print(f\"  🖼️ Visual Context: {'Available' if combined_evaluation['has_visual_context'] else 'Not Available'}\")\n", "    \n", "    return combined_evaluation\n", "\n", "# Bind the visual-enhanced method to the class\n", "SectionWiseHeuristicEvaluationSystem.evaluate_section_with_children_visual = evaluate_section_with_children_visual\n", "\n", "print(\"✅ Visual-enhanced section evaluation method added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visual_enhanced_support_methods"}, "outputs": [], "source": ["# Supporting methods for visual-enhanced section evaluation\n", "def _evaluate_section_with_visual_context(self, section_element_data, section_coord_data, child_elements, show_visual=True):\n", "    \"\"\"Evaluate a section with visual context and child elements\"\"\"\n", "    \n", "    # Create enhanced prompt that includes child element context and visual analysis\n", "    child_context = []\n", "    for child in child_elements:\n", "        child_data = child['data']\n", "        child_context.append({\n", "            'label': child_data.get('label', 'Unknown'),\n", "            'coordinates': child_data.get('coordinates', {}),\n", "            'depth': child.get('depth', 0)\n", "        })\n", "    \n", "    # Enhanced element info with child context and visual data\n", "    enhanced_element_info = {\n", "        \"index\": section_coord_data.get(\"index\", -1),\n", "        \"label\": section_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": section_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": section_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": section_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": section_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": section_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": section_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": section_element_data.get(\"attributes\", {}),\n", "        \"child_elements_context\": child_context,\n", "        \"total_children\": len(child_elements),\n", "        \"is_section\": True,\n", "        \"has_visual_context\": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    }\n", "    \n", "    # Use visual-enhanced evaluation if available\n", "    if enhanced_element_info[\"has_visual_context\"]:\n", "        try:\n", "            # Use the visual-enhanced evaluation method\n", "            evaluation_result = self.evaluator.evaluate_element_with_visual(\n", "                enhanced_element_info, section_coord_data, show_visual=show_visual\n", "            )\n", "            evaluation_result['evaluation_type'] = 'section_with_visual_context'\n", "            return evaluation_result\n", "        except Exception as e:\n", "            print(f\"⚠️ Visual evaluation failed, falling back to standard evaluation: {str(e)}\")\n", "    \n", "    # Fallback to standard evaluation with enhanced context\n", "    prompt = self._create_section_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'section_with_context'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in section evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"section_with_context\"\n", "        }\n", "\n", "def _evaluate_child_with_visual_section_context(self, child_element_data, child_coord_data, section_info, show_visual=True):\n", "    \"\"\"Evaluate a child element with visual context and section context\"\"\"\n", "    \n", "    section_data = section_info['data']\n", "    \n", "    # Enhanced element info with section context and visual data\n", "    enhanced_element_info = {\n", "        \"index\": child_coord_data.get(\"index\", -1),\n", "        \"label\": child_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": child_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": child_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": child_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": child_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": child_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": child_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": child_element_data.get(\"attributes\", {}),\n", "        \"parent_section_context\": {\n", "            'section_label': section_data.get('label', 'Unknown Section'),\n", "            'section_coordinates': section_data.get('coordinates', {}),\n", "            'section_key': section_info['key']\n", "        },\n", "        \"is_child_element\": True,\n", "        \"has_visual_context\": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    }\n", "    \n", "    # Use visual-enhanced evaluation if available\n", "    if enhanced_element_info[\"has_visual_context\"]:\n", "        try:\n", "            # Use the visual-enhanced evaluation method\n", "            evaluation_result = self.evaluator.evaluate_element_with_visual(\n", "                enhanced_element_info, child_coord_data, show_visual=show_visual\n", "            )\n", "            evaluation_result['evaluation_type'] = 'child_with_visual_section_context'\n", "            return evaluation_result\n", "        except Exception as e:\n", "            print(f\"⚠️ Visual evaluation failed for child, falling back to standard evaluation: {str(e)}\")\n", "    \n", "    # Fallback to standard evaluation with enhanced context\n", "    prompt = self._create_child_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'child_with_section_context'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in child evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"child_with_section_context\"\n", "        }\n", "\n", "# Bind the visual-enhanced support methods to the class\n", "SectionWiseHeuristicEvaluationSystem._evaluate_section_with_visual_context = _evaluate_section_with_visual_context\n", "SectionWiseHeuristicEvaluationSystem._evaluate_child_with_visual_section_context = _evaluate_child_with_visual_section_context\n", "\n", "print(\"✅ Visual-enhanced support methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "decision_making_methods"}, "outputs": [], "source": ["# Decision-making methods for evaluation strategy\n", "def _should_evaluate_children_individually(self, section_evaluation, child_elements):\n", "    \"\"\"Determine if child elements need individual evaluation or if section-based evaluation is sufficient\"\"\"\n", "    \n", "    # Criteria for individual child evaluation:\n", "    # 1. Section has high-severity violations that might affect children differently\n", "    # 2. Large number of diverse child elements\n", "    # 3. Section evaluation indicates inconsistencies\n", "    # 4. Child elements have complex interactions\n", "    \n", "    section_violations = section_evaluation.get('violations', [])\n", "    section_score = section_evaluation.get('overall_score', 100)\n", "    \n", "    # Check for high-severity violations in section\n", "    high_severity_violations = [v for v in section_violations if v.get('severity', '').lower() == 'high']\n", "    \n", "    # Check for inconsistency indicators\n", "    inconsistency_keywords = ['inconsistent', 'unclear', 'confusing', 'mixed', 'varied', 'different']\n", "    section_analysis = section_evaluation.get('gemini_analysis', '').lower()\n", "    has_inconsistency_indicators = any(keyword in section_analysis for keyword in inconsistency_keywords)\n", "    \n", "    # Decision criteria\n", "    criteria = {\n", "        'high_severity_violations': len(high_severity_violations) > 0,\n", "        'low_section_score': section_score < 70,\n", "        'many_children': len(child_elements) > 5,\n", "        'diverse_children': len(set(child['data'].get('label', '') for child in child_elements)) > 3,\n", "        'inconsistency_indicators': has_inconsistency_indicators\n", "    }\n", "    \n", "    # Count positive criteria\n", "    positive_criteria = sum(criteria.values())\n", "    \n", "    # Decision logic: evaluate individually if 2 or more criteria are met\n", "    should_evaluate_individually = positive_criteria >= 2\n", "    \n", "    print(f\"    📊 Evaluation Decision Criteria:\")\n", "    for criterion, value in criteria.items():\n", "        status = \"✅\" if value else \"❌\"\n", "        print(f\"      {status} {criterion.replace('_', ' ').title()}: {value}\")\n", "    \n", "    print(f\"    🎯 Decision: {'Individual evaluation' if should_evaluate_individually else 'Section-based evaluation'} ({positive_criteria}/5 criteria met)\")\n", "    \n", "    return should_evaluate_individually\n", "\n", "def _create_child_summary_from_section(self, child, section_evaluation):\n", "    \"\"\"Create a summary evaluation for a child based on section evaluation\"\"\"\n", "    \n", "    child_data = child['data']\n", "    child_label = child_data.get('label', 'Unknown Child')\n", "    \n", "    # Inherit section characteristics but adjust for child-specific factors\n", "    section_score = section_evaluation.get('overall_score', 100)\n", "    section_violations = section_evaluation.get('violations', [])\n", "    \n", "    # Adjust score based on child element type and position\n", "    child_score_adjustment = 0\n", "    \n", "    # Interactive elements might have different usability considerations\n", "    interactive_elements = ['Button', '<PERSON>', 'Input', 'Select', 'Checkbox', 'Radio']\n", "    if any(elem in child_label for elem in interactive_elements):\n", "        child_score_adjustment = -5  # Slightly more critical for interactive elements\n", "    \n", "    # Text elements might be less critical\n", "    text_elements = ['Text', 'Heading', 'Label', 'Paragraph']\n", "    if any(elem in child_label for elem in text_elements):\n", "        child_score_adjustment = 5  # Slightly less critical for text elements\n", "    \n", "    adjusted_score = max(0, min(100, section_score + child_score_adjustment))\n", "    \n", "    # Create child-specific violations based on section violations\n", "    child_violations = []\n", "    for violation in section_violations:\n", "        if violation.get('affects_children', True):  # Assume affects children unless specified\n", "            child_violation = {\n", "                'heuristic': violation.get('heuristic', 'Unknown'),\n", "                'violation': f\"Inherited from section: {violation.get('violation', 'Unknown issue')}\",\n", "                'reason': f\"Child element affected by section-level issue: {violation.get('reason', 'No details')}\",\n", "                'severity': violation.get('severity', 'medium'),\n", "                'recommendation': f\"Address section-level issue affecting this {child_label}\",\n", "                'inherited_from_section': True\n", "            }\n", "            child_violations.append(child_violation)\n", "    \n", "    return {\n", "        'element_info': {\n", "            'label': child_label,\n", "            'coordinates': child_data.get('coordinates', {}),\n", "            'index': child['key']\n", "        },\n", "        'violations': child_violations,\n", "        'passed_checks': section_evaluation.get('passed_checks', []),\n", "        'overall_score': adjusted_score,\n", "        'recommendations': [f\"Ensure {child_label} follows section-wide improvements\"],\n", "        'gemini_analysis': f\"Summary evaluation based on section analysis. {child_label} inherits section characteristics with minor adjustments.\",\n", "        'evaluation_status': 'section_based_summary',\n", "        'evaluation_type': 'child_summary_from_section'\n", "    }\n", "\n", "# Bind the decision-making methods to the class\n", "SectionWiseHeuristicEvaluationSystem._should_evaluate_children_individually = _should_evaluate_children_individually\n", "SectionWiseHeuristicEvaluationSystem._create_child_summary_from_section = _create_child_summary_from_section\n", "\n", "print(\"✅ Decision-making methods for evaluation strategy added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "two_level_evaluation_methods"}, "outputs": [], "source": ["# Two-Level Evaluation Methods\n", "def _evaluate_section_two_level(self, section_element_data, section_coord_data, immediate_children, nested_children_context, show_visual=True):\n", "    \"\"\"Evaluate a section (Level 0) with context of immediate and nested children\"\"\"\n", "    \n", "    # Create enhanced prompt that includes both immediate and nested children context\n", "    immediate_context = []\n", "    for child in immediate_children:\n", "        child_data = child['data']\n", "        immediate_context.append({\n", "            'label': child_data.get('label', 'Unknown'),\n", "            'coordinates': child_data.get('coordinates', {}),\n", "            'evaluation_level': 1,  # Will be evaluated individually\n", "            'depth': child.get('depth', 0)\n", "        })\n", "    \n", "    # Enhanced element info with two-level context and visual data\n", "    enhanced_element_info = {\n", "        \"index\": section_coord_data.get(\"index\", -1),\n", "        \"label\": section_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": section_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": section_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": section_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": section_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": section_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": section_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": section_element_data.get(\"attributes\", {}),\n", "        \"immediate_children_context\": immediate_context,  # Level 1 children\n", "        \"nested_children_context\": nested_children_context,  # Level 2+ children\n", "        \"total_immediate_children\": len(immediate_children),\n", "        \"total_nested_children\": len(nested_children_context),\n", "        \"is_section\": True,\n", "        \"evaluation_level\": 0,  # Level 0 evaluation\n", "        \"has_visual_context\": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    }\n", "    \n", "    # Use visual-enhanced evaluation if available\n", "    if enhanced_element_info[\"has_visual_context\"]:\n", "        try:\n", "            # Use the visual-enhanced evaluation method\n", "            evaluation_result = self.evaluator.evaluate_element_with_visual(\n", "                enhanced_element_info, section_coord_data, show_visual=show_visual\n", "            )\n", "            evaluation_result['evaluation_type'] = 'level0_section_with_visual_context'\n", "            return evaluation_result\n", "        except Exception as e:\n", "            print(f\"⚠️ Visual evaluation failed, falling back to standard evaluation: {str(e)}\")\n", "    \n", "    # Fallback to standard evaluation with enhanced context\n", "    prompt = self._create_level0_section_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'level0_section_evaluation'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in Level 0 section evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"level0_section_evaluation\"\n", "        }\n", "\n", "def _evaluate_level1_child_with_context(self, child_element_data, child_coord_data, section_info, child_nested_context, show_visual=True):\n", "    \"\"\"Evaluate an immediate child (Level 1) with section context and its nested children as context\"\"\"\n", "    \n", "    section_data = section_info['data']\n", "    \n", "    # Enhanced element info with section context, nested children context, and visual data\n", "    enhanced_element_info = {\n", "        \"index\": child_coord_data.get(\"index\", -1),\n", "        \"label\": child_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": child_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": child_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": child_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": child_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": child_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": child_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": child_element_data.get(\"attributes\", {}),\n", "        \"parent_section_context\": {\n", "            'section_label': section_data.get('label', 'Unknown Section'),\n", "            'section_coordinates': section_data.get('coordinates', {}),\n", "            'section_key': section_info['key']\n", "        },\n", "        \"nested_children_context\": child_nested_context,  # Level 2+ children for context\n", "        \"total_nested_children\": len(child_nested_context),\n", "        \"is_immediate_child\": True,\n", "        \"evaluation_level\": 1,  # Level 1 evaluation\n", "        \"has_visual_context\": show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    }\n", "    \n", "    # Use visual-enhanced evaluation if available\n", "    if enhanced_element_info[\"has_visual_context\"]:\n", "        try:\n", "            # Use the visual-enhanced evaluation method\n", "            evaluation_result = self.evaluator.evaluate_element_with_visual(\n", "                enhanced_element_info, child_coord_data, show_visual=show_visual\n", "            )\n", "            evaluation_result['evaluation_type'] = 'level1_child_with_visual_context'\n", "            return evaluation_result\n", "        except Exception as e:\n", "            print(f\"⚠️ Visual evaluation failed for Level 1 child, falling back to standard evaluation: {str(e)}\")\n", "    \n", "    # Fallback to standard evaluation with enhanced context\n", "    prompt = self._create_level1_child_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'level1_child_evaluation'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in Level 1 child evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"level1_child_evaluation\"\n", "        }\n", "\n", "# Bind the two-level evaluation methods to the class\n", "SectionWiseHeuristicEvaluationSystem._evaluate_section_two_level = _evaluate_section_two_level\n", "SectionWiseHeuristicEvaluationSystem._evaluate_level1_child_with_context = _evaluate_level1_child_with_context\n", "\n", "print(\"✅ Two-Level evaluation methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "two_level_prompts"}, "outputs": [], "source": ["# Specialized prompts for two-level evaluation\n", "def _create_level0_section_evaluation_prompt(self, element_info):\n", "    \"\"\"Create evaluation prompt for Level 0 (section) evaluation\"\"\"\n", "    \n", "    element_json = json.dumps(element_info, indent=2)\n", "    \n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a Level 0 (Section-level) heuristic evaluation using Nielsen's 10 usability principles.\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.evaluator.heuristic_principles}\n", "\n", "LEVEL 0 SECTION TO EVALUATE:\n", "{element_json}\n", "\n", "TWO-LEVEL EVALUATION CONTEXT:\n", "• Level 0 (Current): Evaluate the SECTION as a whole organizational unit\n", "• Level 1 (Separate): Immediate children will be evaluated individually\n", "• Level 2+ (Context): Nested children provide context but are NOT evaluated individually\n", "\n", "LEVEL 0 SECTION EVALUATION FOCUS:\n", "Evaluate this section as a CONTAINER and ORGANIZATIONAL UNIT, considering:\n", "\n", "1. **Section Organization**: How well does the section organize and group related content?\n", "2. **Visual Hierarchy**: Does the section create clear visual structure and relationships?\n", "3. **Information Architecture**: Is the section's role in the overall page structure clear?\n", "4. **Section Coherence**: Do all immediate children serve the section's purpose?\n", "5. **Layout Effectiveness**: Does the section layout support user understanding?\n", "6. **Section Consistency**: Are patterns consistent within this section?\n", "7. **Accessibility Structure**: Is the section structure accessible and semantic?\n", "8. **Content Grouping**: Are related elements properly grouped within the section?\n", "\n", "IMPORTANT EVALUATION GUIDELINES:\n", "• Focus on the SECTION'S ORGANIZATIONAL ROLE, not individual element details\n", "• Consider how immediate children work together as a group\n", "• Use nested children as context for understanding section complexity\n", "• Evaluate section-level patterns, consistency, and structure\n", "• Assess the section's contribution to overall page usability\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Section-level organizational issue\",\n", "            \"reason\": \"Detailed explanation focusing on section structure and organization\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Section-level improvement recommendation\",\n", "            \"affects_immediate_children\": \"How this affects Level 1 children evaluation\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this section passes at the organizational level\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief assessment of section's organizational effectiveness\",\n", "    \"key_recommendations\": [\n", "        \"Most important section-level organizational improvements\"\n", "    ],\n", "    \"section_strengths\": [\n", "        \"What this section does well organizationally\"\n", "    ],\n", "    \"immediate_children_guidance\": \"Guidance for evaluating immediate children based on section context\"\n", "}}\n", "\n", "IMPORTANT: Return ONLY valid JSON. Focus on SECTION-LEVEL organizational evaluation.\n", "\"\"\"\n", "    return prompt\n", "\n", "def _create_level1_child_evaluation_prompt(self, element_info):\n", "    \"\"\"Create evaluation prompt for Level 1 (immediate child) evaluation\"\"\"\n", "    \n", "    element_json = json.dumps(element_info, indent=2)\n", "    \n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a Level 1 (Immediate Child) heuristic evaluation using Nielsen's 10 usability principles.\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.evaluator.heuristic_principles}\n", "\n", "LEVEL 1 IMMEDIATE CHILD TO EVALUATE:\n", "{element_json}\n", "\n", "TWO-LEVEL EVALUATION CONTEXT:\n", "• Level 0 (Parent): Section has been evaluated separately as organizational unit\n", "• Level 1 (Current): Evaluate this IMMEDIATE CHILD individually\n", "• Level 2+ (Context): Nested children provide context but are NOT evaluated individually\n", "\n", "LEVEL 1 IMMEDIATE CHILD EVALUATION FOCUS:\n", "Evaluate this immediate child as an INDIVIDUAL FUNCTIONAL ELEMENT, considering:\n", "\n", "1. **Individual Functionality**: Does this element work well on its own?\n", "2. **Section Integration**: How well does it fit within its parent section?\n", "3. **User Interaction**: Are interaction patterns clear and consistent?\n", "4. **Visual Design**: Is the element visually appropriate and accessible?\n", "5. **Content Quality**: Is the content clear, helpful, and well-presented?\n", "6. **Behavioral Consistency**: Does it behave consistently with similar elements?\n", "7. **Accessibility**: Is the element accessible to all users?\n", "8. **Nested Content Context**: How do its nested children support its function?\n", "\n", "IMPORTANT EVALUATION GUIDELINES:\n", "• Focus on this ELEMENT'S INDIVIDUAL USABILITY and functionality\n", "• Consider how it integrates with its parent section context\n", "• Use nested children as context for understanding element complexity\n", "• Evaluate element-specific patterns, interactions, and usability\n", "• Assess the element's individual contribution to user experience\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Individual element usability issue\",\n", "            \"reason\": \"Detailed explanation focusing on element functionality and usability\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Element-specific improvement recommendation\",\n", "            \"section_context_impact\": \"How this relates to the parent section context\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this element passes individually\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief assessment of element's individual usability\",\n", "    \"key_recommendations\": [\n", "        \"Most important element-specific improvements\"\n", "    ],\n", "    \"element_strengths\": [\n", "        \"What this element does well individually\"\n", "    ],\n", "    \"section_integration\": \"How well this element integrates with its parent section\",\n", "    \"nested_children_utilization\": \"How effectively nested children support this element's function\"\n", "}}\n", "\n", "IMPORTANT: Return ONLY valid JSON. Focus on INDIVIDUAL ELEMENT usability evaluation.\n", "\"\"\"\n", "    return prompt\n", "\n", "# Bind the two-level prompt methods to the class\n", "SectionWiseHeuristicEvaluationSystem._create_level0_section_evaluation_prompt = _create_level0_section_evaluation_prompt\n", "SectionWiseHeuristicEvaluationSystem._create_level1_child_evaluation_prompt = _create_level1_child_evaluation_prompt\n", "\n", "print(\"✅ Two-Level evaluation prompt methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "section_wise_methods"}, "outputs": [], "source": ["# Add evaluation methods to SectionWiseHeuristicEvaluationSystem class\n", "def _evaluate_section_with_context(self, section_element_data, section_coord_data, child_elements, show_visual=True):\n", "    \"\"\"Evaluate a section with context of its child elements\"\"\"\n", "    \n", "    # Create enhanced prompt that includes child element context\n", "    child_context = []\n", "    for child in child_elements:\n", "        child_data = child['data']\n", "        child_context.append({\n", "            'label': child_data.get('label', 'Unknown'),\n", "            'coordinates': child_data.get('coordinates', {}),\n", "            'depth': child.get('depth', 0)\n", "        })\n", "    \n", "    # Enhanced element info with child context\n", "    enhanced_element_info = {\n", "        \"index\": section_coord_data.get(\"index\", -1),\n", "        \"label\": section_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": section_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": section_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": section_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": section_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": section_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": section_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": section_element_data.get(\"attributes\", {}),\n", "        \"child_elements_context\": child_context,\n", "        \"total_children\": len(child_elements),\n", "        \"is_section\": True\n", "    }\n", "    \n", "    # Create section-specific evaluation prompt\n", "    prompt = self._create_section_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'section_with_context'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in section evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"section_with_context\"\n", "        }\n", "\n", "def _evaluate_child_with_section_context(self, child_element_data, child_coord_data, section_info, show_visual=True):\n", "    \"\"\"Evaluate a child element with context of its parent section\"\"\"\n", "    \n", "    section_data = section_info['data']\n", "    \n", "    # Enhanced element info with section context\n", "    enhanced_element_info = {\n", "        \"index\": child_coord_data.get(\"index\", -1),\n", "        \"label\": child_coord_data.get(\"label\", \"Unknown\"),\n", "        \"coordinates\": child_coord_data.get(\"coordinates\", {}),\n", "        \"tag\": child_element_data.get(\"tag\", \"unknown\"),\n", "        \"text\": child_element_data.get(\"text\", \"\"),\n", "        \"css_selector\": child_element_data.get(\"cssSelector\", \"\"),\n", "        \"xpath\": child_element_data.get(\"xpath\", \"\"),\n", "        \"computed_style\": child_element_data.get(\"computedStyle\", {}),\n", "        \"attributes\": child_element_data.get(\"attributes\", {}),\n", "        \"parent_section_context\": {\n", "            'section_label': section_data.get('label', 'Unknown Section'),\n", "            'section_coordinates': section_data.get('coordinates', {}),\n", "            'section_key': section_info['key']\n", "        },\n", "        \"is_child_element\": True\n", "    }\n", "    \n", "    # Create child-specific evaluation prompt\n", "    prompt = self._create_child_evaluation_prompt(enhanced_element_info)\n", "    \n", "    try:\n", "        # Get Gemini evaluation\n", "        response = self.evaluator.model.generate_content(prompt)\n", "        \n", "        # Parse response\n", "        evaluation_result = self.evaluator._parse_gemini_response(response.text, enhanced_element_info)\n", "        evaluation_result['evaluation_type'] = 'child_with_section_context'\n", "        \n", "        return evaluation_result\n", "        \n", "    except Exception as e:\n", "        return {\n", "            \"element_info\": enhanced_element_info,\n", "            \"violations\": [],\n", "            \"passed_checks\": [],\n", "            \"overall_score\": 0,\n", "            \"recommendations\": [],\n", "            \"gemini_analysis\": f\"Error in child evaluation: {str(e)}\",\n", "            \"evaluation_status\": \"error\",\n", "            \"evaluation_type\": \"child_with_section_context\"\n", "        }\n", "\n", "# Bind methods to the SectionWiseHeuristicEvaluationSystem class\n", "SectionWiseHeuristicEvaluationSystem._evaluate_section_with_context = _evaluate_section_with_context\n", "SectionWiseHeuristicEvaluationSystem._evaluate_child_with_section_context = _evaluate_child_with_section_context\n", "\n", "print(\"✅ Section-wise evaluation methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "section_wise_prompts"}, "outputs": [], "source": ["# Add prompt creation methods for section-wise evaluation\n", "def _create_section_evaluation_prompt(self, element_info):\n", "    \"\"\"Create evaluation prompt for sections with child element context\"\"\"\n", "    \n", "    element_json = json.dumps(element_info, indent=2)\n", "    \n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a comprehensive heuristic evaluation of a UI SECTION with its child elements.\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.evaluator.heuristic_principles}\n", "\n", "SECTION TO EVALUATE (with child elements context):\n", "{element_json}\n", "\n", "SECTION-WISE EVALUATION TASK:\n", "Analyze this UI section against ALL 10 Nielsen's usability heuristics, considering:\n", "1. The section as a container and organizational unit\n", "2. How well it groups and organizes its child elements\n", "3. The coherence and consistency within the section\n", "4. The section's role in the overall page hierarchy\n", "5. How child elements work together within this section\n", "\n", "SECTION-SPECIFIC EVALUATION FOCUS:\n", "1. **Information Architecture**: How well does the section organize information?\n", "2. **Visual Hierarchy**: Does the section create clear visual relationships?\n", "3. **Functional Grouping**: Are related elements properly grouped?\n", "4. **Section Coherence**: Do all child elements serve the section's purpose?\n", "5. **Navigation & Flow**: How does the section support user navigation?\n", "6. **Consistency**: Are patterns consistent within the section?\n", "7. **Accessibility**: Is the section structure accessible?\n", "8. **Content Organization**: Is content logically organized?\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Brief description of section-level violation\",\n", "            \"reason\": \"Detailed explanation considering section structure and child elements\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Specific recommendation for section improvement\",\n", "            \"affects_children\": \"How this violation impacts child elements\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this section passes\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief assessment of section's organizational effectiveness\",\n", "    \"key_recommendations\": [\n", "        \"Most important section-level improvements\"\n", "    ],\n", "    \"section_strengths\": [\n", "        \"What this section does well in organizing child elements\"\n", "    ]\n", "}}\n", "\n", "IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.\n", "\"\"\"\n", "    return prompt\n", "\n", "def _create_child_evaluation_prompt(self, element_info):\n", "    \"\"\"Create evaluation prompt for child elements with section context\"\"\"\n", "    \n", "    element_json = json.dumps(element_info, indent=2)\n", "    \n", "    prompt = f\"\"\"\n", "You are a UX expert conducting a comprehensive heuristic evaluation of a UI CHILD ELEMENT within its parent section context.\n", "\n", "HEURISTIC EVALUATION PRINCIPLES:\n", "{self.evaluator.heuristic_principles}\n", "\n", "CHILD ELEMENT TO EVALUATE (with section context):\n", "{element_json}\n", "\n", "CHILD ELEMENT EVALUATION TASK:\n", "Analyze this UI child element against ALL 10 Nielsen's usability heuristics, considering:\n", "1. The element's individual usability and functionality\n", "2. How well it fits within its parent section\n", "3. Its relationship to sibling elements in the same section\n", "4. Its contribution to the section's overall purpose\n", "5. Its role in the section's information hierarchy\n", "\n", "CHILD ELEMENT EVALUATION FOCUS:\n", "1. **Individual Functionality**: Does the element work well on its own?\n", "2. **Section Integration**: How well does it integrate with the parent section?\n", "3. **Sibling Relationships**: How does it relate to other elements in the section?\n", "4. **Contextual Appropriateness**: Is it appropriate for its section context?\n", "5. **Hierarchy Contribution**: Does it support the section's information hierarchy?\n", "6. **Interaction Patterns**: Are interaction patterns consistent within the section?\n", "7. **Visual Consistency**: Is it visually consistent with section patterns?\n", "8. **Accessibility**: Is it accessible within the section context?\n", "\n", "RESPONSE FORMAT (JSON):\n", "{{\n", "    \"violations\": [\n", "        {{\n", "            \"heuristic\": \"Heuristic Name\",\n", "            \"violation\": \"Brief description of child element violation\",\n", "            \"reason\": \"Detailed explanation considering section context\",\n", "            \"severity\": \"high|medium|low\",\n", "            \"recommendation\": \"Specific recommendation for child element improvement\",\n", "            \"section_impact\": \"How this violation affects the parent section\"\n", "        }}\n", "    ],\n", "    \"passed_checks\": [\n", "        \"List of heuristic names that this child element passes\"\n", "    ],\n", "    \"overall_score\": 85,\n", "    \"summary\": \"Brief assessment of child element's effectiveness within section\",\n", "    \"key_recommendations\": [\n", "        \"Most important child element improvements\"\n", "    ],\n", "    \"section_integration\": \"How well this element integrates with its parent section\"\n", "}}\n", "\n", "IMPORTANT: Return ONLY valid JSON. No additional text or explanations outside the JSON.\n", "\"\"\"\n", "    return prompt\n", "\n", "# Bind prompt methods to the SectionWiseHeuristicEvaluationSystem class\n", "SectionWiseHeuristicEvaluationSystem._create_section_evaluation_prompt = _create_section_evaluation_prompt\n", "SectionWiseHeuristicEvaluationSystem._create_child_evaluation_prompt = _create_child_evaluation_prompt\n", "\n", "print(\"✅ Section-wise evaluation prompt methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "section_wise_main_methods"}, "outputs": [], "source": ["# Add main evaluation and reporting methods for section-wise system\n", "def perform_section_wise_evaluation(self, show_visual=True):\n", "    \"\"\"Perform comprehensive section-wise heuristic evaluation\"\"\"\n", "    \n", "    if not self.sections:\n", "        print(\"❌ No sections found. Please load hierarchical data first.\")\n", "        return False\n", "    \n", "    print(\"\\n🚀 Starting Section-wise Heuristic Evaluation...\")\n", "    print(\"=\" * 70)\n", "    print(f\"📋 Evaluating {len(self.sections)} sections with their child elements\")\n", "    \n", "    self.section_evaluations = []\n", "    \n", "    for i, section in enumerate(self.sections):\n", "        section_label = section['data'].get('label', 'Unknown Section')\n", "        print(f\"\\n📋 Section {i+1}/{len(self.sections)}: {section_label}\")\n", "        print(\"-\" * 50)\n", "        \n", "        try:\n", "            # Evaluate section with all its children\n", "            section_evaluation = self.evaluate_section_with_children(section, show_visual)\n", "            self.section_evaluations.append(section_evaluation)\n", "            \n", "            print(f\"✅ Section '{section_label}' evaluation completed\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error evaluating section '{section_label}': {str(e)}\")\n", "            continue\n", "    \n", "    print(f\"\\n🎉 Section-wise heuristic evaluation completed!\")\n", "    print(f\"📊 Evaluated {len(self.section_evaluations)} sections\")\n", "    \n", "    # Calculate overall statistics\n", "    total_child_evaluations = sum(len(se['child_evaluations']) for se in self.section_evaluations)\n", "    print(f\"🔍 Total child elements evaluated: {total_child_evaluations}\")\n", "    \n", "    return True\n", "\n", "def generate_section_wise_report(self):\n", "    \"\"\"Generate comprehensive section-wise heuristic evaluation report\"\"\"\n", "    \n", "    if not self.section_evaluations:\n", "        print(\"❌ No section evaluation results available. Please run evaluation first.\")\n", "        return None\n", "    \n", "    print(\"\\n📋 SECTION-WISE HEURISTIC EVALUATION REPORT\")\n", "    print(\"=\" * 80)\n", "    \n", "    # Overall statistics\n", "    total_sections = len(self.section_evaluations)\n", "    total_children = sum(se['total_children'] for se in self.section_evaluations)\n", "    total_violations = sum(se['total_violations'] for se in self.section_evaluations)\n", "    \n", "    avg_section_score = sum(se['section_score'] for se in self.section_evaluations) / total_sections if total_sections > 0 else 0\n", "    avg_child_score = sum(se['average_child_score'] for se in self.section_evaluations) / total_sections if total_sections > 0 else 0\n", "    \n", "    print(f\"\\n📊 OVERALL STATISTICS:\")\n", "    print(f\"   • Sections Evaluated: {total_sections}\")\n", "    print(f\"   • Total Child Elements: {total_children}\")\n", "    print(f\"   • Total Violations Found: {total_violations}\")\n", "    print(f\"   • Average Section Score: {avg_section_score:.1f}/100\")\n", "    print(f\"   • Average Child Elements Score: {avg_child_score:.1f}/100\")\n", "    \n", "    # Section-by-section analysis\n", "    print(f\"\\n🏗️ SECTION-BY-SECTION ANALYSIS:\")\n", "    print(\"-\" * 80)\n", "    \n", "    for i, section_eval in enumerate(self.section_evaluations):\n", "        section_info = section_eval['section_info']\n", "        section_label = section_info['data'].get('label', 'Unknown Section')\n", "        section_score = section_eval['section_score']\n", "        child_score = section_eval['average_child_score']\n", "        total_violations = section_eval['total_violations']\n", "        child_count = section_eval['total_children']\n", "        \n", "        print(f\"\\n{i+1}. 📋 {section_label}\")\n", "        print(f\"   📊 Section Score: {section_score}/100\")\n", "        print(f\"   👥 Child Elements: {child_count} (Avg Score: {child_score:.1f}/100)\")\n", "        print(f\"   🚨 Total Violations: {total_violations}\")\n", "        \n", "        # Section violations\n", "        section_violations = section_eval['section_evaluation'].get('violations', [])\n", "        if section_violations:\n", "            print(f\"   ❌ Section-level Issues ({len(section_violations)}):\")\n", "            for violation in section_violations[:3]:  # Show top 3\n", "                heuristic = violation.get('heuristic', 'Unknown')\n", "                severity = violation.get('severity', 'medium')\n", "                description = violation.get('violation', 'No description')\n", "                print(f\"      • [{severity.upper()}] {heuristic}: {description}\")\n", "        \n", "        # Child element summary\n", "        child_violations = [v for ce in section_eval['child_evaluations'] for v in ce.get('violations', [])]\n", "        if child_violations:\n", "            print(f\"   ⚠️ Child Element Issues ({len(child_violations)}):\")\n", "            # Group by severity\n", "            high_severity = [v for v in child_violations if v.get('severity', '').lower() == 'high']\n", "            medium_severity = [v for v in child_violations if v.get('severity', '').lower() == 'medium']\n", "            low_severity = [v for v in child_violations if v.get('severity', '').lower() == 'low']\n", "            \n", "            print(f\"      • High: {len(high_severity)}, Medium: {len(medium_severity)}, Low: {len(low_severity)}\")\n", "    \n", "    # Top recommendations across all sections\n", "    all_recommendations = []\n", "    for section_eval in self.section_evaluations:\n", "        all_recommendations.extend(section_eval['section_evaluation'].get('recommendations', []))\n", "        for child_eval in section_eval['child_evaluations']:\n", "            all_recommendations.extend(child_eval.get('recommendations', []))\n", "    \n", "    if all_recommendations:\n", "        print(f\"\\n💡 TOP RECOMMENDATIONS:\")\n", "        print(\"-\" * 80)\n", "        for i, rec in enumerate(all_recommendations[:10], 1):  # Top 10\n", "            print(f\"{i}. {rec}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 80)\n", "    print(\"📋 End of Section-wise Heuristic Evaluation Report\")\n", "    \n", "    return {\n", "        'total_sections': total_sections,\n", "        'total_children': total_children,\n", "        'total_violations': total_violations,\n", "        'average_section_score': avg_section_score,\n", "        'average_child_score': avg_child_score,\n", "        'section_evaluations': self.section_evaluations\n", "    }\n", "\n", "# Enhanced visual evaluation method\n", "def perform_visual_enhanced_section_wise_evaluation(self, show_visual=True):\n", "    \"\"\"Perform comprehensive visual-enhanced section-wise heuristic evaluation\"\"\"\n", "    \n", "    if not self.sections:\n", "        print(\"❌ No sections found. Please load hierarchical data first.\")\n", "        return False\n", "    \n", "    print(\"\\n🚀 Starting Visual-Enhanced Section-wise Heuristic Evaluation...\")\n", "    print(\"=\" * 80)\n", "    print(f\"📋 Evaluating {len(self.sections)} sections with their child elements\")\n", "    \n", "    # Check visual analysis availability\n", "    has_visual = show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    \n", "    if has_visual:\n", "        print(\"🖼️ Visual analysis enabled - Sections and children will be highlighted and analyzed visually\")\n", "    else:\n", "        print(\"📊 Visual analysis not available - Using JSON data only\")\n", "    \n", "    print(\"\\n🎯 Enhanced Evaluation Features:\")\n", "    print(\"   • Section-wise visual closeups and analysis\")\n", "    print(\"   • Child element visual highlighting when needed\")\n", "    print(\"   • Intelligent decision-making for individual vs. section-based evaluation\")\n", "    print(\"   • Combined visual and JSON data analysis\")\n", "    \n", "    self.section_evaluations = []\n", "    \n", "    for i, section in enumerate(self.sections):\n", "        section_label = section['data'].get('label', 'Unknown Section')\n", "        print(f\"\\n📋 Section {i+1}/{len(self.sections)}: {section_label}\")\n", "        print(\"-\" * 60)\n", "        \n", "        try:\n", "            # Use visual-enhanced evaluation method\n", "            section_evaluation = self.evaluate_section_with_children_visual(section, has_visual)\n", "            self.section_evaluations.append(section_evaluation)\n", "            \n", "            print(f\"✅ Section '{section_label}' evaluation completed\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error evaluating section '{section_label}': {str(e)}\")\n", "            continue\n", "    \n", "    print(f\"\\n🎉 Visual-enhanced section-wise heuristic evaluation completed!\")\n", "    print(f\"📊 Evaluated {len(self.section_evaluations)} sections\")\n", "    \n", "    # Calculate overall statistics\n", "    total_child_evaluations = sum(len(se['child_evaluations']) for se in self.section_evaluations)\n", "    individual_evaluations = sum(1 for se in self.section_evaluations if se.get('evaluation_strategy') == 'individual_children')\n", "    section_based_evaluations = len(self.section_evaluations) - individual_evaluations\n", "    \n", "    print(f\"🔍 Total child elements evaluated: {total_child_evaluations}\")\n", "    print(f\"🎯 Individual child evaluations: {individual_evaluations} sections\")\n", "    print(f\"📋 Section-based evaluations: {section_based_evaluations} sections\")\n", "    print(f\"🖼️ Visual analysis used: {'Yes' if has_visual else 'No'}\")\n", "    \n", "    return True\n", "\n", "# Bind methods to the SectionWiseHeuristicEvaluationSystem class\n", "SectionWiseHeuristicEvaluationSystem.perform_section_wise_evaluation = perform_section_wise_evaluation\n", "SectionWiseHeuristicEvaluationSystem.perform_visual_enhanced_section_wise_evaluation = perform_visual_enhanced_section_wise_evaluation\n", "SectionWiseHeuristicEvaluationSystem.generate_section_wise_report = generate_section_wise_report\n", "\n", "print(\"✅ Section-wise main evaluation and reporting methods added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "two_level_main_method"}, "outputs": [], "source": ["# Two-Level Visual-Enhanced Evaluation Method\n", "def perform_two_level_visual_enhanced_evaluation(self, show_visual=True):\n", "    \"\"\"Perform comprehensive two-level visual-enhanced heuristic evaluation\"\"\"\n", "    \n", "    if not self.sections:\n", "        print(\"❌ No sections found. Please load hierarchical data first.\")\n", "        return False\n", "    \n", "    print(\"\\n🚀 Starting Two-Level Visual-Enhanced Heuristic Evaluation...\")\n", "    print(\"=\" * 80)\n", "    print(f\"📋 Evaluating {len(self.sections)} sections using two-level approach\")\n", "    \n", "    # Check visual analysis availability\n", "    has_visual = show_visual and hasattr(self.evaluator, 'visual_analyzer') and self.evaluator.visual_analyzer.screenshot is not None\n", "    \n", "    if has_visual:\n", "        print(\"🖼️ Visual analysis enabled - Sections and immediate children will be highlighted and analyzed visually\")\n", "    else:\n", "        print(\"📊 Visual analysis not available - Using JSON data only\")\n", "    \n", "    print(\"\\n🎯 Two-Level Evaluation Approach:\")\n", "    print(\"   📋 Level 0: Evaluate each section as an organizational unit\")\n", "    print(\"   🔍 Level 1: Evaluate immediate children individually\")\n", "    print(\"   📝 Level 2+: Use nested children as context only (not evaluated individually)\")\n", "    print(\"   🖼️ Visual closeups and analysis for both levels\")\n", "    print(\"   📊 Combined visual and JSON data analysis\")\n", "    \n", "    self.section_evaluations = []\n", "    \n", "    for i, section in enumerate(self.sections):\n", "        section_label = section['data'].get('label', 'Unknown Section')\n", "        print(f\"\\n📋 Section {i+1}/{len(self.sections)}: {section_label}\")\n", "        print(\"=\" * 70)\n", "        \n", "        try:\n", "            # Use two-level evaluation method\n", "            section_evaluation = self.evaluate_section_two_level(section, has_visual)\n", "            self.section_evaluations.append(section_evaluation)\n", "            \n", "            print(f\"✅ Two-level evaluation for '{section_label}' completed\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error in two-level evaluation for section '{section_label}': {str(e)}\")\n", "            continue\n", "    \n", "    print(f\"\\n🎉 Two-Level visual-enhanced heuristic evaluation completed!\")\n", "    print(f\"📊 Evaluated {len(self.section_evaluations)} sections\")\n", "    \n", "    # Calculate two-level statistics\n", "    total_level1_evaluations = sum(se['total_level1_children'] for se in self.section_evaluations)\n", "    total_context_children = sum(se['total_context_children'] for se in self.section_evaluations)\n", "    avg_level0_score = sum(se['level0_score'] for se in self.section_evaluations) / len(self.section_evaluations) if self.section_evaluations else 0\n", "    avg_level1_score = sum(se['average_level1_score'] for se in self.section_evaluations) / len(self.section_evaluations) if self.section_evaluations else 0\n", "    \n", "    print(f\"\\n📊 Two-Level Evaluation Statistics:\")\n", "    print(f\"   📋 Level 0 (Sections): {len(self.section_evaluations)} evaluated\")\n", "    print(f\"   🔍 Level 1 (Immediate Children): {total_level1_evaluations} evaluated\")\n", "    print(f\"   📝 Level 2+ (Context Children): {total_context_children} used for context\")\n", "    print(f\"   📊 Average Level 0 Score: {avg_level0_score:.1f}/100\")\n", "    print(f\"   📊 Average Level 1 Score: {avg_level1_score:.1f}/100\")\n", "    print(f\"   🖼️ Visual analysis used: {'Yes' if has_visual else 'No'}\")\n", "    \n", "    return True\n", "\n", "# Bind the two-level method to the class\n", "SectionWiseHeuristicEvaluationSystem.perform_two_level_visual_enhanced_evaluation = perform_two_level_visual_enhanced_evaluation\n", "\n", "print(\"✅ Two-Level visual-enhanced evaluation method added!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "two_level_reporting"}, "outputs": [], "source": ["# Two-Level Compatible Reporting Method\n", "def generate_two_level_report(self):\n", "    \"\"\"Generate comprehensive two-level heuristic evaluation report\"\"\"\n", "    \n", "    if not self.section_evaluations:\n", "        print(\"❌ No evaluation results available. Please run evaluation first.\")\n", "        return None\n", "    \n", "    # Detect evaluation type\n", "    is_two_level = any('evaluation_approach' in se and se['evaluation_approach'] == 'two_level' for se in self.section_evaluations)\n", "    \n", "    if is_two_level:\n", "        print(\"\\n📋 TWO-LEVEL HEURISTIC EVALUATION REPORT\")\n", "        print(\"=\" * 80)\n", "        \n", "        # Two-level statistics\n", "        total_sections = len(self.section_evaluations)\n", "        total_level1_children = sum(se.get('total_level1_children', 0) for se in self.section_evaluations)\n", "        total_context_children = sum(se.get('total_context_children', 0) for se in self.section_evaluations)\n", "        total_violations = sum(se.get('total_violations', 0) for se in self.section_evaluations)\n", "        \n", "        avg_level0_score = sum(se.get('level0_score', 0) for se in self.section_evaluations) / total_sections if total_sections > 0 else 0\n", "        avg_level1_score = sum(se.get('average_level1_score', 0) for se in self.section_evaluations) / total_sections if total_sections > 0 else 0\n", "        \n", "        print(f\"\\n📊 TWO-LEVEL EVALUATION STATISTICS:\")\n", "        print(f\"   📋 Level 0 (Sections): {total_sections} evaluated\")\n", "        print(f\"   🔍 Level 1 (Immediate Children): {total_level1_children} evaluated\")\n", "        print(f\"   📝 Level 2+ (Context Children): {total_context_children} used for context\")\n", "        print(f\"   📊 Average Level 0 Score: {avg_level0_score:.1f}/100\")\n", "        print(f\"   📊 Average Level 1 Score: {avg_level1_score:.1f}/100\")\n", "        print(f\"   🚨 Total Violations Found: {total_violations}\")\n", "        \n", "        # Section-by-section two-level analysis\n", "        print(f\"\\n🏗️ SECTION-BY-SECTION TWO-LEVEL ANALYSIS:\")\n", "        print(\"-\" * 80)\n", "        \n", "        for i, section_eval in enumerate(self.section_evaluations):\n", "            section_info = section_eval['section_info']\n", "            section_label = section_info['data'].get('label', 'Unknown Section')\n", "            level0_score = section_eval.get('level0_score', 0)\n", "            level1_score = section_eval.get('average_level1_score', 0)\n", "            total_violations = section_eval.get('total_violations', 0)\n", "            level1_count = section_eval.get('total_level1_children', 0)\n", "            context_count = section_eval.get('total_context_children', 0)\n", "            \n", "            print(f\"\\n{i+1}. 📋 {section_label}\")\n", "            print(f\"   📋 Level 0 (Section) Score: {level0_score}/100\")\n", "            print(f\"   🔍 Level 1 (Immediate Children): {level1_count} elements (Avg Score: {level1_score:.1f}/100)\")\n", "            print(f\"   📝 Level 2+ (Context): {context_count} nested children\")\n", "            print(f\"   🚨 Total Violations: {total_violations}\")\n", "            \n", "            # Level 0 violations\n", "            level0_evaluation = section_eval.get('level0_evaluation', {})\n", "            level0_violations = level0_evaluation.get('violations', [])\n", "            if level0_violations:\n", "                print(f\"   ❌ Level 0 Issues ({len(level0_violations)}):\")\n", "                for violation in level0_violations[:3]:  # Show top 3\n", "                    heuristic = violation.get('heuristic', 'Unknown')\n", "                    severity = violation.get('severity', 'medium')\n", "                    description = violation.get('violation', 'No description')\n", "                    print(f\"      • [{severity.upper()}] {heuristic}: {description}\")\n", "            \n", "            # Level 1 violations summary\n", "            level1_evaluations = section_eval.get('level1_evaluations', [])\n", "            level1_violations = [v for le in level1_evaluations for v in le.get('violations', [])]\n", "            if level1_violations:\n", "                print(f\"   ⚠️ Level 1 Issues ({len(level1_violations)}):\")\n", "                # Group by severity\n", "                high_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'high']\n", "                medium_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'medium']\n", "                low_severity = [v for v in level1_violations if v.get('severity', '').lower() == 'low']\n", "                \n", "                print(f\"      • High: {len(high_severity)}, Medium: {len(medium_severity)}, Low: {len(low_severity)}\")\n", "        \n", "        # Top recommendations across all levels\n", "        all_recommendations = []\n", "        for section_eval in self.section_evaluations:\n", "            # Level 0 recommendations\n", "            level0_eval = section_eval.get('level0_evaluation', {})\n", "            all_recommendations.extend(level0_eval.get('recommendations', []))\n", "            all_recommendations.extend(level0_eval.get('key_recommendations', []))\n", "            \n", "            # Level 1 recommendations\n", "            for level1_eval in section_eval.get('level1_evaluations', []):\n", "                all_recommendations.extend(level1_eval.get('recommendations', []))\n", "                all_recommendations.extend(level1_eval.get('key_recommendations', []))\n", "        \n", "        if all_recommendations:\n", "            print(f\"\\n💡 TOP RECOMMENDATIONS (All Levels):\")\n", "            print(\"-\" * 80)\n", "            for i, rec in enumerate(all_recommendations[:15], 1):  # Top 15\n", "                print(f\"{i}. {rec}\")\n", "        \n", "        print(\"\\n\" + \"=\" * 80)\n", "        print(\"📋 End of Two-Level Heuristic Evaluation Report\")\n", "        \n", "        return {\n", "            'evaluation_type': 'two_level',\n", "            'total_sections': total_sections,\n", "            'total_level1_children': total_level1_children,\n", "            'total_context_children': total_context_children,\n", "            'total_violations': total_violations,\n", "            'average_level0_score': avg_level0_score,\n", "            'average_level1_score': avg_level1_score,\n", "            'section_evaluations': self.section_evaluations\n", "        }\n", "    \n", "    else:\n", "        # Fall back to legacy reporting for non-two-level evaluations\n", "        return self.generate_section_wise_report()\n", "\n", "# Enhanced generate_section_wise_report to handle both types\n", "def generate_compatible_report(self):\n", "    \"\"\"Generate report compatible with both legacy and two-level evaluations\"\"\"\n", "    \n", "    if not self.section_evaluations:\n", "        print(\"❌ No evaluation results available. Please run evaluation first.\")\n", "        return None\n", "    \n", "    # Check if this is a two-level evaluation\n", "    is_two_level = any('evaluation_approach' in se and se['evaluation_approach'] == 'two_level' for se in self.section_evaluations)\n", "    \n", "    if is_two_level:\n", "        return self.generate_two_level_report()\n", "    else:\n", "        return self.generate_section_wise_report()\n", "\n", "# Bind the new reporting methods to the class\n", "SectionWiseHeuristicEvaluationSystem.generate_two_level_report = generate_two_level_report\n", "SectionWiseHeuristicEvaluationSystem.generate_compatible_report = generate_compatible_report\n", "\n", "print(\"✅ Two-Level compatible reporting methods added!\")"]}, {"cell_type": "markdown", "metadata": {"id": "initialization"}, "source": ["## 🚀 Enhanced Section-wise System Initialization and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "initialize_section_wise_system"}, "outputs": [], "source": ["# Initialize the Enhanced Section-wise Heuristic Evaluation System\n", "print(\"🚀 Initializing Section-wise Heuristic Evaluation System\")\n", "print(\"=\" * 70)\n", "\n", "# Create section-wise system instance\n", "section_wise_system = SectionWiseHeuristicEvaluationSystem()\n", "\n", "# Enhance evaluator with visual analysis capabilities\n", "section_wise_system.evaluator.enhance_with_visual_analysis()\n", "\n", "# Load hierarchical data with two-level structure from configuration files or fallback\n", "section_wise_system.load_two_level_hierarchical_data()\n", "\n", "print(\"\\n🎉 Two-Level section-wise system ready for hierarchical heuristic evaluation!\")\n", "print(\"\\n📋 Available sections for two-level evaluation:\")\n", "for i, section in enumerate(section_wise_system.sections):\n", "    section_label = section['data'].get('label', 'Unknown Section')\n", "    immediate_count = len(get_immediate_children_for_evaluation(section))\n", "    context_count = len(get_nested_children_context(section))\n", "    x = section['data'].get('coordinates', {}).get('x', 0)\n", "    y = section['data'].get('coordinates', {}).get('y', 0)\n", "    print(f\"  {i+1}. {section_label} at ({x}, {y})\")\n", "    print(f\"     📋 Level 0: Section evaluation\")\n", "    print(f\"     🔍 Level 1: {immediate_count} immediate children (evaluated individually)\")\n", "    print(f\"     📝 Level 2+: {context_count} nested children (context only)\")\n", "\n", "print(\"\\n💡 Two-Level Evaluation System:\")\n", "print(\"   📋 Level 0: Each section evaluated as an organizational unit\")\n", "print(\"   🔍 Level 1: Immediate children evaluated individually with section context\")\n", "print(\"   📝 Level 2+: Nested children used as context to improve evaluation accuracy\")"]}, {"cell_type": "markdown", "metadata": {"id": "screenshot_loading"}, "source": ["## 🖼️ Load Screenshot for Visual Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_screenshot"}, "outputs": [], "source": ["# Load screenshot for visual-enhanced heuristic evaluation\n", "print(\"🖼️ Loading Screenshot for Visual Analysis\")\n", "print(\"=\" * 50)\n", "\n", "# Try to load screenshot from configuration path\n", "screenshot_loaded = section_wise_system.evaluator.load_screenshot_for_evaluation()\n", "\n", "if screenshot_loaded:\n", "    print(\"\\n✅ Screenshot loaded successfully!\")\n", "    print(\"🎯 Visual-enhanced heuristic evaluation is now available.\")\n", "    print(\"📊 Elements will be highlighted and cropped during evaluation.\")\n", "else:\n", "    print(\"\\n⚠️ Screenshot not found at default path.\")\n", "    print(\"💡 You can upload a screenshot using the interface below.\")\n", "    print(\"🔄 Evaluation will proceed with technical data only.\")\n", "\n", "# Create screenshot upload interface\n", "def upload_screenshot():\n", "    \"\"\"Upload screenshot for visual analysis\"\"\"\n", "    print(\"\\n📁 Upload Screenshot Image\")\n", "    print(\"=\" * 30)\n", "    print(\"Please upload a screenshot image (PNG, JPG, etc.) of the web page.\")\n", "    \n", "    # Upload screenshot file\n", "    uploaded = files.upload()\n", "    \n", "    if uploaded:\n", "        try:\n", "            # Get the uploaded filename\n", "            screenshot_filename = list(uploaded.keys())[0]\n", "            \n", "            # Load the screenshot\n", "            success = heuristic_system.evaluator.load_screenshot_for_evaluation(screenshot_filename)\n", "            \n", "            if success:\n", "                print(f\"\\n✅ Screenshot '{screenshot_filename}' loaded successfully!\")\n", "                print(\"🎯 Visual-enhanced evaluation is now ready.\")\n", "                \n", "                # Display a preview of the screenshot\n", "                screenshot = heuristic_system.evaluator.visual_analyzer.screenshot\n", "                plt.figure(figsize=(12, 8))\n", "                plt.imshow(screenshot)\n", "                plt.title(f\"Loaded Screenshot: {screenshot_filename}\")\n", "                plt.axis('off')\n", "                plt.show()\n", "                \n", "                return True\n", "            else:\n", "                print(f\"\\n❌ Failed to load screenshot '{screenshot_filename}'\")\n", "                return False\n", "                \n", "        except Exception as e:\n", "            print(f\"\\n❌ Error processing screenshot: {str(e)}\")\n", "            return False\n", "    else:\n", "        print(\"\\n⚠️ No file uploaded.\")\n", "        return False\n", "\n", "# Create upload button for screenshot\n", "screenshot_button = widgets.Button(\n", "    description='🖼️ Upload Screenshot',\n", "    button_style='success',\n", "    layout=widgets.Layout(width='200px')\n", ")\n", "\n", "screenshot_output = widgets.Output()\n", "\n", "def on_screenshot_click(button):\n", "    with screenshot_output:\n", "        clear_output(wait=True)\n", "        upload_screenshot()\n", "\n", "screenshot_button.on_click(on_screenshot_click)\n", "\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>🖼️ Screenshot Upload</h3>\"),\n", "    widgets.HTML(\"<p>Upload a screenshot to enable visual-enhanced heuristic evaluation.</p>\"),\n", "    screenshot_button,\n", "    screenshot_output\n", "]))"]}, {"cell_type": "markdown", "metadata": {"id": "run_evaluation"}, "source": ["## 🔍 Run Section-wise Heuristic Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "perform_section_wise_evaluation"}, "outputs": [], "source": ["# Perform comprehensive section-wise heuristic evaluation\n", "print(\"🔍 Starting Section-wise Heuristic Evaluation\")\n", "print(\"=\" * 70)\n", "print(\"This will evaluate each section and its child elements against Nielsen's 10 usability heuristics using Gemini AI.\")\n", "\n", "# Check if visual analysis is available\n", "has_visual = hasattr(section_wise_system.evaluator, 'visual_analyzer') and section_wise_system.evaluator.visual_analyzer.screenshot is not None\n", "\n", "if has_visual:\n", "    print(\"🖼️ Visual analysis enabled - Sections and elements will be highlighted and analyzed visually.\")\n", "else:\n", "    print(\"📊 Visual analysis not available - Using technical data only.\")\n", "\n", "print(\"\\n🏗️ Section-wise evaluation approach:\")\n", "print(\"   1. Evaluate each section as an organizational unit\")\n", "print(\"   2. Evaluate child elements within their section context\")\n", "print(\"   3. Consider hierarchical relationships and dependencies\")\n", "print(\"   4. Generate comprehensive section-wise reports\")\n", "\n", "print(\"\\nPlease wait while the section-wise evaluation is performed...\\n\")\n", "\n", "# Run the two-level visual-enhanced evaluation\n", "success = section_wise_system.perform_two_level_visual_enhanced_evaluation(show_visual=has_visual)\n", "\n", "if success:\n", "    print(\"\\n✅ Section-wise heuristic evaluation completed successfully!\")\n", "    print(\"📋 Generating comprehensive section-wise report...\")\n", "else:\n", "    print(\"\\n❌ Section-wise heuristic evaluation failed. Please check the data and try again.\")"]}, {"cell_type": "markdown", "metadata": {"id": "view_report"}, "source": ["## 📋 View Section-wise Evaluation Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_section_wise_report"}, "outputs": [], "source": ["# Generate and display comprehensive two-level heuristic evaluation report\n", "report = section_wise_system.generate_two_level_report()\n", "\n", "if report:\n", "    if report.get('evaluation_type') == 'two_level':\n", "        print(\"\\n🎯 TWO-LEVEL EVALUATION SUMMARY:\")\n", "        print(f\"Average Level 0 (Section) Score: {report['average_level0_score']:.1f}/100\")\n", "        print(f\"Average Level 1 (Immediate Children) Score: {report['average_level1_score']:.1f}/100\")\n", "        \n", "        # Overall assessment based on combined scores\n", "        combined_score = (report['average_level0_score'] + report['average_level1_score']) / 2\n", "        \n", "        if combined_score >= 80:\n", "            print(\"🟢 Excellent two-level organization and usability - Minor improvements needed\")\n", "        elif combined_score >= 60:\n", "            print(\"🟡 Good two-level structure with some issues - Improvements recommended\")\n", "        elif combined_score >= 40:\n", "            print(\"🟠 Fair two-level organization - Significant improvements needed\")\n", "        else:\n", "            print(\"🔴 Poor two-level structure and usability - Major improvements required\")\n", "        \n", "        print(f\"\\n📊 Two-Level Detailed Statistics:\")\n", "        print(f\"   📋 Level 0 (Sections): {report['total_sections']} evaluated\")\n", "        print(f\"   🔍 Level 1 (Immediate Children): {report['total_level1_children']} evaluated\")\n", "        print(f\"   📝 Level 2+ (Context Children): {report['total_context_children']} used for context\")\n", "        print(f\"   🚨 Total Violations Found: {report['total_violations']}\")\n", "        print(f\"   📊 Average Level 1 Children per Section: {report['total_level1_children'] / report['total_sections']:.1f}\")\n", "        print(f\"   📝 Average Context Children per Section: {report['total_context_children'] / report['total_sections']:.1f}\")\n", "    else:\n", "        # Legacy report format\n", "        print(\"\\n🎯 SECTION-WISE EVALUATION SUMMARY:\")\n", "        print(f\"Average Section Score: {report.get('average_section_score', 0):.1f}/100\")\n", "        print(f\"Average Child Elements Score: {report.get('average_child_score', 0):.1f}/100\")\n", "        \n", "        print(f\"\\n📊 Detailed Statistics:\")\n", "        print(f\"   • Total Sections Evaluated: {report.get('total_sections', 0)}\")\n", "        print(f\"   • Total Child Elements: {report.get('total_children', 0)}\")\n", "        print(f\"   • Total Violations Found: {report.get('total_violations', 0)}\")\n", "    \n", "else:\n", "    print(\"❌ Could not generate evaluation report. Please run evaluation first.\")"]}, {"cell_type": "markdown", "metadata": {"id": "vision_demo"}, "source": ["## 🖼️ Vision-Enhanced Evaluation Demo\n", "\n", "Demonstration of the new vision-enabled functionality that sends images to Gemini!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vision_demo_code"}, "outputs": [], "source": ["# 🖼️ VISION-<PERSON><PERSON><PERSON><PERSON>ED EVALUATION DEMONSTRATION\n", "print(\"🚀 Vision-Enhanced Heuristic Evaluation Demo\")\n", "print(\"=\" * 60)\n", "\n", "# Check if system is ready\n", "if 'section_wise_system' in locals():\n", "    evaluator = section_wise_system.evaluator\n", "    \n", "    # Configure vision settings\n", "    print(\"\\n🔧 Configuring Vision Settings:\")\n", "    evaluator.configure_vision(\n", "        enable_vision=True,\n", "        image_quality=\"high\",\n", "        max_image_size=(1024, 1024)\n", "    )\n", "    \n", "    # Load screenshot\n", "    print(\"\\n📸 Loading Screenshot:\")\n", "    screenshot_loaded = evaluator.load_screenshot_for_evaluation()\n", "    \n", "    if screenshot_loaded:\n", "        print(\"\\n🎯 Running Vision-Enhanced Evaluation on Sample Element:\")\n", "        \n", "        # Get a sample element for demonstration\n", "        if hasattr(section_wise_system, 'coordinates') and section_wise_system.coordinates:\n", "            sample_coord = section_wise_system.coordinates[0]\n", "            sample_element = section_wise_system.element_info.get(str(sample_coord['index']), {})\n", "            \n", "            print(f\"📋 Evaluating: {sample_coord.get('label', 'Unknown Element')}\")\n", "            print(f\"🔍 Index: {sample_coord.get('index', 'N/A')}\")\n", "            \n", "            # Run vision-enhanced evaluation\n", "            result = evaluator.evaluate_element_with_visual(\n", "                sample_element, \n", "                sample_coord, \n", "                show_visual=True\n", "            )\n", "            \n", "            # Display results\n", "            print(\"\\n📊 VISION-ENHANCED EVALUATION RESULTS:\")\n", "            print(\"=\" * 50)\n", "            print(f\"🎯 Overall Score: {result.get('overall_score', 0)}/100\")\n", "            print(f\"🖼️ Vision Enabled: {result.get('vision_enabled', False)}\")\n", "            print(f\"📸 Images Sent to Gemini: {result.get('images_sent_to_gemini', False)}\")\n", "            print(f\"🔍 Has Visual Context: {result.get('has_visual_context', False)}\")\n", "            \n", "            if result.get('images_sent_to_gemini', False):\n", "                print(\"\\n✅ SUCCESS: Images were successfully sent to Gemini for analysis!\")\n", "                print(\"🎉 The model now has visual clarity about the UI element!\")\n", "                \n", "                # Show image analysis if available\n", "                if 'image_analysis' in result:\n", "                    print(f\"\\n🖼️ Image Analysis: {result['image_analysis']}\")\n", "                \n", "                # Show visual assessment\n", "                if 'visual_assessment' in result:\n", "                    print(f\"\\n👁️ Visual Assessment: {result['visual_assessment']}\")\n", "            else:\n", "                print(\"\\n⚠️ Images were not sent to Gemini. Check vision configuration.\")\n", "            \n", "            print(\"\\n🎊 Vision-enhanced evaluation completed successfully!\")\n", "            print(\"📈 The Gemini model now has comprehensive visual understanding!\")\n", "        else:\n", "            print(\"❌ No sample elements available for demonstration\")\n", "    else:\n", "        print(\"❌ Could not load screenshot. Please check the screenshot file path.\")\n", "else:\n", "    print(\"❌ Section-wise system not initialized. Please run the initialization cells first.\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"🎯 VISION FEATURES SUMMARY:\")\n", "print(\"   📸 Full screenshot with highlighted elements sent to Gemini\")\n", "print(\"   🔍 Close-up crops of individual elements sent to Gemini\")\n", "print(\"   🤖 Enhanced prompts for multimodal analysis\")\n", "print(\"   📊 Vision status tracking in all evaluation results\")\n", "print(\"   🎨 Configurable image quality and size settings\")"]}, {"cell_type": "markdown", "metadata": {"id": "file_upload"}, "source": ["## 📁 Upload Your Own Data\n", "\n", "You can upload your own UI data files to analyze custom web pages!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_interface"}, "outputs": [], "source": ["def upload_custom_data():\n", "    \"\"\"Upload and process custom UI data files for heuristic evaluation\"\"\"\n", "    \n", "    print(\"📁 Upload Your Custom UI Data Files\")\n", "    print(\"=\" * 50)\n", "    print(\"Please upload the following files:\")\n", "    print(\"1. coordinates.json - UI element positions and labels\")\n", "    print(\"2. element_info.json - Complete DOM data for elements\")\n", "    print(\"\\nNote: Files should follow the same format as the sample data.\")\n", "    \n", "    # Upload coordinates file\n", "    print(\"\\n📍 Upload coordinates.json:\")\n", "    coord_files = files.upload()\n", "    \n", "    # Upload element info file\n", "    print(\"\\n🏗️ Upload element_info.json:\")\n", "    element_files = files.upload()\n", "    \n", "    try:\n", "        # Process uploaded files\n", "        coord_filename = list(coord_files.keys())[0]\n", "        element_filename = list(element_files.keys())[0]\n", "        \n", "        # Load the data\n", "        with open(coord_filename, 'r') as f:\n", "            custom_coordinates = json.load(f)\n", "        \n", "        with open(element_filename, 'r') as f:\n", "            custom_element_info = json.load(f)\n", "        \n", "        print(f\"\\n✅ Files loaded successfully!\")\n", "        print(f\"📊 Coordinates: {len(custom_coordinates)} elements\")\n", "        print(f\"🏗️ Element info: {len(custom_element_info)} elements\")\n", "        \n", "        # Initialize system with custom data\n", "        print(\"\\n🔄 Initializing system with your data...\")\n", "        heuristic_system.load_data(custom_coordinates, custom_element_info)\n", "        \n", "        print(\"\\n🎉 Your custom data is now ready for heuristic evaluation!\")\n", "        print(\"Run the evaluation cells above to analyze your UI elements.\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error processing uploaded files: {str(e)}\")\n", "        print(\"Please check that your files are in the correct JSON format.\")\n", "        return False\n", "\n", "# Create upload button\n", "upload_button = widgets.Button(\n", "    description='📁 Upload Custom Data',\n", "    button_style='info',\n", "    layout=widgets.Layout(width='200px')\n", ")\n", "\n", "upload_output = widgets.Output()\n", "\n", "def on_upload_click(button):\n", "    with upload_output:\n", "        clear_output(wait=True)\n", "        upload_custom_data()\n", "\n", "upload_button.on_click(on_upload_click)\n", "\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>📁 Custom Data Upload</h3>\"),\n", "    widgets.HTML(\"<p>Upload your own coordinates.json and element_info.json files to analyze custom web pages.</p>\"),\n", "    upload_button,\n", "    upload_output\n", "]))"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}